# Loni Frontend

A comprehensive Next.js application with shadcn/ui integration, centralized design system, and production-ready Docker configuration.

## 🚀 Features

- **Next.js 15.4.5** with App Router
- **shadcn/ui** - Complete component library (50+ components)
- **Tailwind CSS v4** - Utility-first CSS framework
- **Centralized Design System** - Consistent design tokens and theming
- **React Context/Providers** - Theme and design system distribution
- **TypeScript** - Type-safe development
- **Bun** - Fast package manager and runtime
- **Docker** - Production-ready containerization
- **Dark/Light Mode** - Theme switching with next-themes
- **Responsive Design** - Mobile-first approach
- **OKLCH Color Space** - Modern color definitions
- **Geist Fonts** - Typography with <PERSON><PERSON><PERSON> and <PERSON>eist Mono

## 📦 Tech Stack

- **Framework**: Next.js 15.4.5
- **Package Manager**: Bun
- **Styling**: Tailwind CSS v4
- **Components**: shadcn/ui
- **Icons**: Lucide React
- **Fonts**: Geist <PERSON>s & Geist Mono
- **Theme**: next-themes
- **Language**: TypeScript
- **Container**: Docker

## 🛠️ Installation

### Prerequisites

- [Bun](https://bun.sh/) (v1.2.19 or later)
- Node.js (v18 or later)
- Docker (optional, for containerization)

### Setup

1. **Install dependencies**:
   ```bash
   bun install
   ```

2. **Run development server**:
   ```bash
   bun dev
   ```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
apps/frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/health/         # Health check endpoint
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout
│   │   └── page.tsx            # Home page
│   ├── components/
│   │   ├── providers/          # React providers
│   │   │   └── theme-provider.tsx
│   │   └── ui/                 # shadcn/ui components
│   └── lib/
│       ├── design-system.ts    # Design tokens
│       ├── theme-config.ts     # Theme configuration
│       └── utils.ts            # Utility functions
├── public/                     # Static assets
├── components.json             # shadcn/ui configuration
├── next.config.ts              # Next.js configuration
├── tailwind.config.ts          # Tailwind configuration
├── Dockerfile                  # Docker configuration
├── .dockerignore              # Docker ignore rules
└── package.json               # Dependencies
```

## 🎨 Design System

The application features a comprehensive design system with:

### Design Tokens
- **Typography**: Display, heading, body, and caption scales
- **Colors**: OKLCH-based color palette with semantic tokens
- **Spacing**: Consistent spacing scale
- **Border Radius**: Rounded corner system
- **Shadows**: Elevation system
- **Animations**: Smooth transitions and micro-interactions
- **Breakpoints**: Mobile-first responsive design
- **Z-Index**: Layering system

### Theme Configuration
- Light and dark themes
- System preference detection
- Smooth theme transitions
- CSS custom properties
- Theme persistence

### Component Variants
- Button variants and sizes
- Card styles
- Input states
- Badge variants

## 🧩 Available Components

All shadcn/ui components are included:

- Accordion, Alert, Alert Dialog
- Avatar, Badge, Breadcrumb
- Button, Calendar, Card
- Carousel, Chart, Checkbox
- Collapsible, Combobox, Command
- Context Menu, Data Table, Date Picker
- Dialog, Drawer, Dropdown Menu
- Form, Hover Card, Input
- Input OTP, Label, Menubar
- Navigation Menu, Pagination, Popover
- Progress, Radio Group, Resizable
- Scroll Area, Select, Separator
- Sheet, Sidebar, Skeleton
- Slider, Sonner, Switch
- Table, Tabs, Textarea
- Toggle, Toggle Group, Tooltip
- Typography

## 🐳 Docker

### Development
```bash
# Build development image
docker build -t loni-frontend:dev .

# Run container
docker run -p 3000:3000 loni-frontend:dev
```

### Production
```bash
# Build production image
docker build -t loni-frontend:prod .

# Run production container
docker run -p 3000:3000 loni-frontend:prod
```

### Health Check
The application includes a health check endpoint at `/api/health` that provides:
- Application status
- Uptime information
- Memory usage
- Environment details

## 📱 Responsive Design

The application is built with a mobile-first approach:

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

Responsive utilities and hooks are available through the design system provider.

## 🔧 Development

### Available Scripts

```bash
# Development server
bun dev

# Build for production
bun build

# Start production server
bun start

# Type checking
bun run type-check

# Linting
bun run lint
```

### Adding New Components

1. **Add shadcn/ui component**:
   ```bash
   bunx shadcn@latest add [component-name]
   ```

2. **Custom components**: Place in `src/components/`

3. **Update design system**: Modify `src/lib/design-system.ts`

## 🌙 Theme System

The theme system provides:

- **useTheme**: Theme switching and detection
- **useDesignTokens**: Access to design system tokens
- **useResponsive**: Responsive utilities and breakpoint detection

### Usage Example

```tsx
import { useTheme, useDesignTokens, useResponsive } from '@/components/providers/theme-provider'

function MyComponent() {
  const { theme, setTheme } = useTheme()
  const tokens = useDesignTokens()
  const { isMobile, isTablet, isDesktop } = useResponsive()

  return (
    <div className={`p-${tokens.spacing.md}`}>
      Current theme: {theme}
    </div>
  )
}
```

## 🚀 Deployment

The application is optimized for production deployment with:

- Standalone output for Docker
- Image optimization
- Bundle splitting
- Security headers
- Performance optimizations

## 📄 License

This project is part of the Loni application suite.

## 🤝 Contributing

1. Follow the established code style
2. Use Bun for package management
3. Maintain design system consistency
4. Test responsive behavior
5. Update documentation as needed
