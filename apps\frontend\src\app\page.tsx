"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useTheme, useDesignTokens, useResponsive } from "@/components/providers/theme-provider"
import { Moon, Sun, Palette, Code, Smartphone, Monitor, Tablet } from "lucide-react"

export default function Home() {
  const { mode, setTheme, resolvedTheme } = useTheme()
  const tokens = useDesignTokens()
  const responsive = useResponsive()

  const toggleTheme = () => {
    setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <a className="mr-6 flex items-center space-x-2" href="/">
              <Palette className="h-6 w-6" />
              <span className="font-bold">Loni Design System</span>
            </a>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="w-full flex-1 md:w-auto md:flex-none">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleTheme}
                className="h-9 w-9"
              >
                <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                <span className="sr-only">Toggle theme</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container py-8">
        {/* Hero Section */}
        <section className="text-center py-12">
          <h1 className="text-display-1 font-bold mb-4">
            Welcome to Loni
          </h1>
          <p className="text-body-large text-muted-foreground mb-8 max-w-2xl mx-auto">
            A comprehensive Next.js application with shadcn/ui integration, centralized design system,
            and production-ready Docker configuration.
          </p>
          <div className="flex gap-4 justify-center flex-wrap">
            <Button size="lg">Get Started</Button>
            <Button variant="outline" size="lg">
              <Code className="mr-2 h-4 w-4" />
              View Components
            </Button>
          </div>
        </section>

        {/* Features Grid */}
        <section className="py-12">
          <h2 className="text-heading-1 text-center mb-8">Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Design System
                </CardTitle>
                <CardDescription>
                  Centralized design tokens and theme configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-body-small text-muted-foreground">
                  Complete design system with typography, colors, spacing, and component variants.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  shadcn/ui Components
                </CardTitle>
                <CardDescription>
                  All 50+ shadcn/ui components included
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-body-small text-muted-foreground">
                  Pre-built, accessible components ready for production use.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  Responsive Design
                </CardTitle>
                <CardDescription>
                  Mobile-first responsive design system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-body-small text-muted-foreground">
                  Optimized for all screen sizes with responsive utilities.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Design System Demo */}
        <section className="py-12">
          <h2 className="text-heading-1 text-center mb-8">Design System Demo</h2>

          <Tabs defaultValue="colors" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="colors">Colors</TabsTrigger>
              <TabsTrigger value="typography">Typography</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
              <TabsTrigger value="responsive">Responsive</TabsTrigger>
            </TabsList>

            <TabsContent value="colors" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Color Palette</CardTitle>
                  <CardDescription>
                    Current theme: {resolvedTheme}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <div className="h-16 bg-primary rounded-md"></div>
                      <p className="text-sm font-medium">Primary</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 bg-secondary rounded-md"></div>
                      <p className="text-sm font-medium">Secondary</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 bg-accent rounded-md"></div>
                      <p className="text-sm font-medium">Accent</p>
                    </div>
                    <div className="space-y-2">
                      <div className="h-16 bg-muted rounded-md"></div>
                      <p className="text-sm font-medium">Muted</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="typography" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Typography Scale</CardTitle>
                  <CardDescription>
                    Consistent typography system
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h1 className="text-display-1">Display 1</h1>
                    <h2 className="text-display-2">Display 2</h2>
                    <h3 className="text-heading-1">Heading 1</h3>
                    <h4 className="text-heading-2">Heading 2</h4>
                    <p className="text-body">Body text with normal weight</p>
                    <p className="text-body-small">Small body text</p>
                    <p className="text-caption">Caption text</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="components" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Component Showcase</CardTitle>
                  <CardDescription>
                    Interactive components from shadcn/ui
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex flex-wrap gap-2">
                    <Button>Default</Button>
                    <Button variant="secondary">Secondary</Button>
                    <Button variant="outline">Outline</Button>
                    <Button variant="ghost">Ghost</Button>
                    <Button variant="destructive">Destructive</Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <Badge>Default</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge variant="outline">Outline</Badge>
                    <Badge variant="destructive">Destructive</Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" placeholder="Enter your email" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="notifications" />
                      <Label htmlFor="notifications">Enable notifications</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="responsive" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Responsive Information</CardTitle>
                  <CardDescription>
                    Current viewport and breakpoint information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Viewport Size</h4>
                      <p className="text-sm text-muted-foreground">
                        Width: {responsive.windowSize.width}px<br />
                        Height: {responsive.windowSize.height}px
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Current Breakpoint</h4>
                      <div className="flex gap-2 flex-wrap">
                        <Badge variant={responsive.isMobile ? "default" : "outline"}>
                          <Smartphone className="mr-1 h-3 w-3" />
                          Mobile
                        </Badge>
                        <Badge variant={responsive.isTablet ? "default" : "outline"}>
                          <Tablet className="mr-1 h-3 w-3" />
                          Tablet
                        </Badge>
                        <Badge variant={responsive.isDesktop ? "default" : "outline"}>
                          <Monitor className="mr-1 h-3 w-3" />
                          Desktop
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
          <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
            <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
              Built with Next.js, shadcn/ui, and Tailwind CSS. Powered by Bun.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
