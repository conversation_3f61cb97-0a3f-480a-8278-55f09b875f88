"use client"

import { useEffect, useState } from 'react'

export interface StyleConfig {
  theme: {
    mode: 'light' | 'dark' | 'system'
    primaryColor: string
    secondaryColor: string
    accentColor: string
    backgroundColor: string
    foregroundColor: string
  }
  typography: {
    fontFamily: string
    fontSize: {
      base: number
      scale: number
    }
    lineHeight: {
      tight: number
      normal: number
      relaxed: number
    }
    fontWeight: {
      normal: number
      medium: number
      semibold: number
      bold: number
    }
  }
  spacing: {
    scale: number
    baseUnit: number
  }
  borderRadius: {
    sm: number
    md: number
    lg: number
    xl: number
  }
  animations: {
    duration: {
      fast: number
      normal: number
      slow: number
    }
    easing: string
  }
}

export const defaultStyleConfig: StyleConfig = {
  theme: {
    mode: 'system',
    primaryColor: '#0f172a',
    secondaryColor: '#64748b',
    accentColor: '#3b82f6',
    backgroundColor: '#ffffff',
    foregroundColor: '#0f172a'
  },
  typography: {
    fontFamily: 'Inter',
    fontSize: {
      base: 16,
      scale: 1.125
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    }
  },
  spacing: {
    scale: 1,
    baseUnit: 4
  },
  borderRadius: {
    sm: 2,
    md: 6,
    lg: 8,
    xl: 12
  },
  animations: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500
    },
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
}

const STORAGE_KEY = 'loni-style-config'

export class StyleConfigManager {
  private static instance: StyleConfigManager
  private config: StyleConfig
  private listeners: Set<(config: StyleConfig) => void> = new Set()

  private constructor() {
    this.config = this.loadConfig()
  }

  static getInstance(): StyleConfigManager {
    if (!StyleConfigManager.instance) {
      StyleConfigManager.instance = new StyleConfigManager()
    }
    return StyleConfigManager.instance
  }

  private loadConfig(): StyleConfig {
    if (typeof window === 'undefined') {
      return defaultStyleConfig
    }

    try {
      const saved = localStorage.getItem(STORAGE_KEY)
      if (saved) {
        return { ...defaultStyleConfig, ...JSON.parse(saved) }
      }
    } catch (error) {
      console.error('Failed to load style config:', error)
    }
    
    return defaultStyleConfig
  }

  getConfig(): StyleConfig {
    return this.config
  }

  updateConfig(newConfig: StyleConfig): void {
    this.config = newConfig
    this.saveConfig()
    this.applyConfig()
    this.notifyListeners()
  }

  private saveConfig(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.config))
    } catch (error) {
      console.error('Failed to save style config:', error)
    }
  }

  private applyConfig(): void {
    if (typeof window === 'undefined') return

    const root = document.documentElement

    // Apply CSS custom properties
    root.style.setProperty('--primary', this.config.theme.primaryColor)
    root.style.setProperty('--secondary', this.config.theme.secondaryColor)
    root.style.setProperty('--accent', this.config.theme.accentColor)
    root.style.setProperty('--background', this.config.theme.backgroundColor)
    root.style.setProperty('--foreground', this.config.theme.foregroundColor)

    // Apply typography
    root.style.setProperty('--font-family', this.config.typography.fontFamily)
    root.style.setProperty('--font-size-base', `${this.config.typography.fontSize.base}px`)
    root.style.setProperty('--font-scale', this.config.typography.fontSize.scale.toString())

    // Apply spacing
    root.style.setProperty('--spacing-base', `${this.config.spacing.baseUnit}px`)
    root.style.setProperty('--spacing-scale', this.config.spacing.scale.toString())

    // Apply border radius
    root.style.setProperty('--radius-sm', `${this.config.borderRadius.sm}px`)
    root.style.setProperty('--radius-md', `${this.config.borderRadius.md}px`)
    root.style.setProperty('--radius-lg', `${this.config.borderRadius.lg}px`)
    root.style.setProperty('--radius-xl', `${this.config.borderRadius.xl}px`)

    // Apply animations
    root.style.setProperty('--duration-fast', `${this.config.animations.duration.fast}ms`)
    root.style.setProperty('--duration-normal', `${this.config.animations.duration.normal}ms`)
    root.style.setProperty('--duration-slow', `${this.config.animations.duration.slow}ms`)
    root.style.setProperty('--easing', this.config.animations.easing)
  }

  subscribe(listener: (config: StyleConfig) => void): () => void {
    this.listeners.add(listener)
    return () => {
      this.listeners.delete(listener)
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.config))
  }

  reset(): void {
    this.config = defaultStyleConfig
    this.saveConfig()
    this.applyConfig()
    this.notifyListeners()
  }

  exportConfig(): string {
    return JSON.stringify(this.config, null, 2)
  }

  importConfig(configString: string): boolean {
    try {
      const importedConfig = JSON.parse(configString)
      this.updateConfig({ ...defaultStyleConfig, ...importedConfig })
      return true
    } catch (error) {
      console.error('Failed to import config:', error)
      return false
    }
  }
}

export function useStyleConfig() {
  const [config, setConfig] = useState<StyleConfig>(() => 
    StyleConfigManager.getInstance().getConfig()
  )

  useEffect(() => {
    const manager = StyleConfigManager.getInstance()
    const unsubscribe = manager.subscribe(setConfig)
    
    // Apply config on mount
    manager['applyConfig']()
    
    return unsubscribe
  }, [])

  const updateConfig = (newConfig: StyleConfig) => {
    StyleConfigManager.getInstance().updateConfig(newConfig)
  }

  const resetConfig = () => {
    StyleConfigManager.getInstance().reset()
  }

  const exportConfig = () => {
    return StyleConfigManager.getInstance().exportConfig()
  }

  const importConfig = (configString: string) => {
    return StyleConfigManager.getInstance().importConfig(configString)
  }

  return {
    config,
    updateConfig,
    resetConfig,
    exportConfig,
    importConfig
  }
}
