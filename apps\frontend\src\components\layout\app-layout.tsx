"use client"

import { Navbar } from "./navbar"
import { Sidebar } from "./sidebar"
import { cn } from "@/lib/utils"

interface AppLayoutProps {
  children: React.ReactNode
  className?: string
}

export function AppLayout({ children, className }: AppLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Fixed Navbar */}
      <Navbar />
      
      {/* Fixed Sidebar */}
      <Sidebar />
      
      {/* Main Content Area */}
      <main 
        className={cn(
          "ml-64 mt-16 min-h-[calc(100vh-4rem)]",
          "transition-all duration-300 ease-in-out",
          className
        )}
      >
        <div className="container mx-auto p-6">
          {children}
        </div>
      </main>
    </div>
  )
}
