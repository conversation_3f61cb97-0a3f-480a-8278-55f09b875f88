/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 20V10", key: "g8npz5" }],
  ["path", { d: "M18 20v-4", key: "8uic4z" }],
  ["path", { d: "M6 20V4", key: "1w1bmo" }]
];
const ChartNoAxesColumnDecreasing = createLucideIcon("chart-no-axes-column-decreasing", __iconNode);

export { __iconNode, ChartNoAxesColumnDecreasing as default };
//# sourceMappingURL=chart-no-axes-column-decreasing.js.map
