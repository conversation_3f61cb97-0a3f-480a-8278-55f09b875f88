"use client"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Home,
  Palette,
  Settings,
  Users,
  BarChart3,
  FileText,
  Image,
  Layout,
  Layers,
  Zap,
  ChevronRight,
  Folder,
  FolderOpen
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"

interface SidebarItem {
  title: string
  href?: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string
  children?: SidebarItem[]
}

const sidebarItems: SidebarItem[] = [
  {
    title: "Dashboard",
    href: "/",
    icon: Home,
  },
  {
    title: "Style Configuration",
    href: "/style-config",
    icon: Palette,
    badge: "New"
  },
  {
    title: "Components",
    icon: Layout,
    children: [
      {
        title: "UI Components",
        href: "/components/ui",
        icon: Layers,
      },
      {
        title: "Forms",
        href: "/components/forms",
        icon: FileText,
      },
      {
        title: "Data Display",
        href: "/components/data",
        icon: BarChart3,
      },
    ]
  },
  {
    title: "Design System",
    icon: Zap,
    children: [
      {
        title: "Typography",
        href: "/design/typography",
        icon: FileText,
      },
      {
        title: "Colors",
        href: "/design/colors",
        icon: Palette,
      },
      {
        title: "Spacing",
        href: "/design/spacing",
        icon: Layout,
      },
    ]
  },
  {
    title: "Media",
    href: "/media",
    icon: Image,
  },
  {
    title: "Users",
    href: "/users",
    icon: Users,
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart3,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
]

interface SidebarItemProps {
  item: SidebarItem
  level?: number
}

function SidebarItemComponent({ item, level = 0 }: SidebarItemProps) {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const hasChildren = item.children && item.children.length > 0
  const isActive = item.href === pathname
  const isChildActive = item.children?.some(child => child.href === pathname)

  const handleClick = () => {
    if (hasChildren) {
      setIsOpen(!isOpen)
    }
  }

  const ItemContent = (
    <div
      className={cn(
        "flex items-center justify-between w-full px-3 py-2 text-sm rounded-md transition-colors",
        "hover:bg-accent hover:text-accent-foreground",
        isActive && "bg-accent text-accent-foreground",
        isChildActive && "bg-accent/50",
        level > 0 && "ml-4"
      )}
    >
      <div className="flex items-center space-x-3">
        <item.icon className="h-4 w-4" />
        <span>{item.title}</span>
        {item.badge && (
          <Badge variant="secondary" className="text-xs">
            {item.badge}
          </Badge>
        )}
      </div>
      {hasChildren && (
        <ChevronRight
          className={cn(
            "h-4 w-4 transition-transform",
            isOpen && "rotate-90"
          )}
        />
      )}
    </div>
  )

  return (
    <div>
      {item.href ? (
        <Link href={item.href}>
          {ItemContent}
        </Link>
      ) : (
        <button onClick={handleClick} className="w-full text-left">
          {ItemContent}
        </button>
      )}
      
      {hasChildren && isOpen && (
        <div className="mt-1 space-y-1">
          {item.children?.map((child, index) => (
            <SidebarItemComponent
              key={index}
              item={child}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function Sidebar() {
  return (
    <div className="fixed left-0 top-16 bottom-0 w-64 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <ScrollArea className="h-full py-6 px-4">
        <div className="space-y-2">
          <div className="px-3 py-2">
            <h2 className="mb-2 px-0 text-lg font-semibold tracking-tight">
              Navigation
            </h2>
          </div>
          <Separator className="my-4" />
          <div className="space-y-1">
            {sidebarItems.map((item, index) => (
              <SidebarItemComponent key={index} item={item} />
            ))}
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
