/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M2 12a10 10 0 1 1 10 10", key: "1yn6ov" }],
  ["path", { d: "m2 22 10-10", key: "28ilpk" }],
  ["path", { d: "M8 22H2v-6", key: "sulq54" }]
];
const CircleArrowOutDownLeft = createLucideIcon("circle-arrow-out-down-left", __iconNode);

export { __iconNode, CircleArrowOutDownLeft as default };
//# sourceMappingURL=circle-arrow-out-down-left.js.map
