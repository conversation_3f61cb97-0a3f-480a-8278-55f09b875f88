{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/lib/design-system.ts"], "sourcesContent": ["/**\n * Centralized Design System Configuration\n * All design tokens, theme configuration, and styling constants\n */\n\n// Typography Configuration\nexport const typography = {\n  fontFamilies: {\n    sans: ['var(--font-geist-sans)', 'system-ui', 'sans-serif'],\n    mono: ['var(--font-geist-mono)', 'Consolas', 'monospace'],\n  },\n  fontSizes: {\n    xs: '0.75rem',     // 12px\n    sm: '0.875rem',    // 14px\n    base: '1rem',      // 16px\n    lg: '1.125rem',    // 18px\n    xl: '1.25rem',     // 20px\n    '2xl': '1.5rem',   // 24px\n    '3xl': '1.875rem', // 30px\n    '4xl': '2.25rem',  // 36px\n    '5xl': '3rem',     // 48px\n    '6xl': '3.75rem',  // 60px\n    '7xl': '4.5rem',   // 72px\n    '8xl': '6rem',     // 96px\n    '9xl': '8rem',     // 128px\n  },\n  fontWeights: {\n    thin: '100',\n    extralight: '200',\n    light: '300',\n    normal: '400',\n    medium: '500',\n    semibold: '600',\n    bold: '700',\n    extrabold: '800',\n    black: '900',\n  },\n  lineHeights: {\n    none: '1',\n    tight: '1.25',\n    snug: '1.375',\n    normal: '1.5',\n    relaxed: '1.625',\n    loose: '2',\n  },\n  letterSpacing: {\n    tighter: '-0.05em',\n    tight: '-0.025em',\n    normal: '0em',\n    wide: '0.025em',\n    wider: '0.05em',\n    widest: '0.1em',\n  },\n} as const;\n\n// Color Palette Configuration\nexport const colors = {\n  // Primary brand colors\n  primary: {\n    50: 'oklch(0.985 0.001 106.423)',\n    100: 'oklch(0.97 0.001 106.424)',\n    200: 'oklch(0.923 0.003 48.717)',\n    300: 'oklch(0.709 0.01 56.259)',\n    400: 'oklch(0.553 0.013 58.071)',\n    500: 'oklch(0.216 0.006 56.043)',\n    600: 'oklch(0.147 0.004 49.25)',\n    700: 'oklch(0.147 0.004 49.25)',\n    800: 'oklch(0.147 0.004 49.25)',\n    900: 'oklch(0.147 0.004 49.25)',\n    950: 'oklch(0.147 0.004 49.25)',\n  },\n  // Secondary colors\n  secondary: {\n    50: 'oklch(0.985 0.001 106.423)',\n    100: 'oklch(0.97 0.001 106.424)',\n    200: 'oklch(0.923 0.003 48.717)',\n    300: 'oklch(0.709 0.01 56.259)',\n    400: 'oklch(0.553 0.013 58.071)',\n    500: 'oklch(0.268 0.007 34.298)',\n    600: 'oklch(0.216 0.006 56.043)',\n    700: 'oklch(0.147 0.004 49.25)',\n    800: 'oklch(0.147 0.004 49.25)',\n    900: 'oklch(0.147 0.004 49.25)',\n    950: 'oklch(0.147 0.004 49.25)',\n  },\n  // Accent colors\n  accent: {\n    50: 'oklch(0.985 0.001 106.423)',\n    100: 'oklch(0.97 0.001 106.424)',\n    200: 'oklch(0.923 0.003 48.717)',\n    300: 'oklch(0.709 0.01 56.259)',\n    400: 'oklch(0.553 0.013 58.071)',\n    500: 'oklch(0.268 0.007 34.298)',\n    600: 'oklch(0.216 0.006 56.043)',\n    700: 'oklch(0.147 0.004 49.25)',\n    800: 'oklch(0.147 0.004 49.25)',\n    900: 'oklch(0.147 0.004 49.25)',\n    950: 'oklch(0.147 0.004 49.25)',\n  },\n  // Neutral colors\n  neutral: {\n    50: 'oklch(0.985 0.001 106.423)',\n    100: 'oklch(0.97 0.001 106.424)',\n    200: 'oklch(0.923 0.003 48.717)',\n    300: 'oklch(0.709 0.01 56.259)',\n    400: 'oklch(0.553 0.013 58.071)',\n    500: 'oklch(0.268 0.007 34.298)',\n    600: 'oklch(0.216 0.006 56.043)',\n    700: 'oklch(0.147 0.004 49.25)',\n    800: 'oklch(0.147 0.004 49.25)',\n    900: 'oklch(0.147 0.004 49.25)',\n    950: 'oklch(0.147 0.004 49.25)',\n  },\n  // Semantic colors\n  success: {\n    50: 'oklch(0.95 0.05 142)',\n    100: 'oklch(0.9 0.1 142)',\n    200: 'oklch(0.85 0.15 142)',\n    300: 'oklch(0.8 0.2 142)',\n    400: 'oklch(0.75 0.25 142)',\n    500: 'oklch(0.7 0.3 142)',\n    600: 'oklch(0.65 0.35 142)',\n    700: 'oklch(0.6 0.4 142)',\n    800: 'oklch(0.55 0.45 142)',\n    900: 'oklch(0.5 0.5 142)',\n    950: 'oklch(0.45 0.55 142)',\n  },\n  warning: {\n    50: 'oklch(0.95 0.05 85)',\n    100: 'oklch(0.9 0.1 85)',\n    200: 'oklch(0.85 0.15 85)',\n    300: 'oklch(0.8 0.2 85)',\n    400: 'oklch(0.75 0.25 85)',\n    500: 'oklch(0.7 0.3 85)',\n    600: 'oklch(0.65 0.35 85)',\n    700: 'oklch(0.6 0.4 85)',\n    800: 'oklch(0.55 0.45 85)',\n    900: 'oklch(0.5 0.5 85)',\n    950: 'oklch(0.45 0.55 85)',\n  },\n  error: {\n    50: 'oklch(0.95 0.05 27)',\n    100: 'oklch(0.9 0.1 27)',\n    200: 'oklch(0.85 0.15 27)',\n    300: 'oklch(0.8 0.2 27)',\n    400: 'oklch(0.75 0.25 27)',\n    500: 'oklch(0.577 0.245 27.325)',\n    600: 'oklch(0.65 0.35 27)',\n    700: 'oklch(0.6 0.4 27)',\n    800: 'oklch(0.55 0.45 27)',\n    900: 'oklch(0.5 0.5 27)',\n    950: 'oklch(0.45 0.55 27)',\n  },\n} as const;\n\n// Spacing Scale Configuration\nexport const spacing = {\n  px: '1px',\n  0: '0px',\n  0.5: '0.125rem',   // 2px\n  1: '0.25rem',      // 4px\n  1.5: '0.375rem',   // 6px\n  2: '0.5rem',       // 8px\n  2.5: '0.625rem',   // 10px\n  3: '0.75rem',      // 12px\n  3.5: '0.875rem',   // 14px\n  4: '1rem',         // 16px\n  5: '1.25rem',      // 20px\n  6: '1.5rem',       // 24px\n  7: '1.75rem',      // 28px\n  8: '2rem',         // 32px\n  9: '2.25rem',      // 36px\n  10: '2.5rem',      // 40px\n  11: '2.75rem',     // 44px\n  12: '3rem',        // 48px\n  14: '3.5rem',      // 56px\n  16: '4rem',        // 64px\n  20: '5rem',        // 80px\n  24: '6rem',        // 96px\n  28: '7rem',        // 112px\n  32: '8rem',        // 128px\n  36: '9rem',        // 144px\n  40: '10rem',       // 160px\n  44: '11rem',       // 176px\n  48: '12rem',       // 192px\n  52: '13rem',       // 208px\n  56: '14rem',       // 224px\n  60: '15rem',       // 240px\n  64: '16rem',       // 256px\n  72: '18rem',       // 288px\n  80: '20rem',       // 320px\n  96: '24rem',       // 384px\n} as const;\n\n// Border Radius Configuration\nexport const borderRadius = {\n  none: '0px',\n  sm: 'calc(var(--radius) - 4px)',\n  md: 'calc(var(--radius) - 2px)',\n  lg: 'var(--radius)',\n  xl: 'calc(var(--radius) + 4px)',\n  '2xl': 'calc(var(--radius) + 8px)',\n  '3xl': 'calc(var(--radius) + 12px)',\n  full: '9999px',\n} as const;\n\n// Shadow Configuration\nexport const shadows = {\n  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',\n  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',\n  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',\n  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',\n  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',\n  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',\n  none: '0 0 #0000',\n} as const;\n\n// Animation Configuration\nexport const animations = {\n  durations: {\n    75: '75ms',\n    100: '100ms',\n    150: '150ms',\n    200: '200ms',\n    300: '300ms',\n    500: '500ms',\n    700: '700ms',\n    1000: '1000ms',\n  },\n  timingFunctions: {\n    linear: 'linear',\n    in: 'cubic-bezier(0.4, 0, 1, 1)',\n    out: 'cubic-bezier(0, 0, 0.2, 1)',\n    'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',\n  },\n} as const;\n\n// Breakpoints Configuration\nexport const breakpoints = {\n  sm: '640px',\n  md: '768px',\n  lg: '1024px',\n  xl: '1280px',\n  '2xl': '1536px',\n} as const;\n\n// Z-Index Configuration\nexport const zIndex = {\n  auto: 'auto',\n  0: '0',\n  10: '10',\n  20: '20',\n  30: '30',\n  40: '40',\n  50: '50',\n  dropdown: '1000',\n  sticky: '1020',\n  fixed: '1030',\n  modal: '1040',\n  popover: '1050',\n  tooltip: '1060',\n  toast: '1070',\n} as const;\n\n// Component Variants Configuration\nexport const componentVariants = {\n  button: {\n    sizes: {\n      sm: 'h-8 px-3 text-xs',\n      md: 'h-9 px-4 py-2',\n      lg: 'h-10 px-8',\n      xl: 'h-11 px-8',\n      icon: 'h-9 w-9',\n    },\n    variants: {\n      default: 'bg-primary text-primary-foreground shadow hover:bg-primary/90',\n      destructive: 'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',\n      outline: 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',\n      secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground',\n      link: 'text-primary underline-offset-4 hover:underline',\n    },\n  },\n  input: {\n    sizes: {\n      sm: 'h-8 px-3 text-xs',\n      md: 'h-9 px-3',\n      lg: 'h-10 px-3',\n    },\n  },\n  card: {\n    variants: {\n      default: 'border bg-card text-card-foreground shadow',\n      elevated: 'border bg-card text-card-foreground shadow-lg',\n      outline: 'border-2 bg-card text-card-foreground',\n    },\n  },\n} as const;\n\n// Export the complete design system\nexport const designSystem = {\n  typography,\n  colors,\n  spacing,\n  borderRadius,\n  shadows,\n  animations,\n  breakpoints,\n  zIndex,\n  componentVariants,\n} as const;\n\nexport type DesignSystem = typeof designSystem;\nexport type Typography = typeof typography;\nexport type Colors = typeof colors;\nexport type Spacing = typeof spacing;\nexport type BorderRadius = typeof borderRadius;\nexport type Shadows = typeof shadows;\nexport type Animations = typeof animations;\nexport type Breakpoints = typeof breakpoints;\nexport type ZIndex = typeof zIndex;\nexport type ComponentVariants = typeof componentVariants;\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2BAA2B;;;;;;;;;;;;;AACpB,MAAM,aAAa;IACxB,cAAc;QACZ,MAAM;YAAC;YAA0B;YAAa;SAAa;QAC3D,MAAM;YAAC;YAA0B;YAAY;SAAY;IAC3D;IACA,WAAW;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;QACN,WAAW;QACX,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IACA,eAAe;QACb,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAGO,MAAM,SAAS;IACpB,uBAAuB;IACvB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,mBAAmB;IACnB,WAAW;QACT,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,gBAAgB;IAChB,QAAQ;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,iBAAiB;IACjB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,kBAAkB;IAClB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,OAAO;QACL,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,UAAU;IACrB,IAAI;IACJ,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAGO,MAAM,eAAe;IAC1B,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,MAAM;AACR;AAGO,MAAM,UAAU;IACrB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,MAAM;AACR;AAGO,MAAM,aAAa;IACxB,WAAW;QACT,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,MAAM;IACR;IACA,iBAAiB;QACf,QAAQ;QACR,IAAI;QACJ,KAAK;QACL,UAAU;IACZ;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,SAAS;IACpB,MAAM;IACN,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,UAAU;IACV,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAGO,MAAM,oBAAoB;IAC/B,QAAQ;QACN,OAAO;YACL,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;QACA,UAAU;YACR,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;IACF;IACA,OAAO;QACL,OAAO;YACL,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,MAAM;QACJ,UAAU;YACR,SAAS;YACT,UAAU;YACV,SAAS;QACX;IACF;AACF;AAGO,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/lib/theme-config.ts"], "sourcesContent": ["/**\n * Theme Configuration\n * Defines light and dark theme configurations using the design system\n */\n\nimport { designSystem } from './design-system';\n\n// Theme mode type\nexport type ThemeMode = 'light' | 'dark' | 'system';\n\n// CSS Custom Properties for themes\nexport const lightTheme = {\n  // Base colors\n  '--background': 'oklch(1 0 0)',\n  '--foreground': 'oklch(0.147 0.004 49.25)',\n  \n  // Card colors\n  '--card': 'oklch(1 0 0)',\n  '--card-foreground': 'oklch(0.147 0.004 49.25)',\n  \n  // Popover colors\n  '--popover': 'oklch(1 0 0)',\n  '--popover-foreground': 'oklch(0.147 0.004 49.25)',\n  \n  // Primary colors\n  '--primary': 'oklch(0.216 0.006 56.043)',\n  '--primary-foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Secondary colors\n  '--secondary': 'oklch(0.97 0.001 106.424)',\n  '--secondary-foreground': 'oklch(0.216 0.006 56.043)',\n  \n  // Muted colors\n  '--muted': 'oklch(0.97 0.001 106.424)',\n  '--muted-foreground': 'oklch(0.553 0.013 58.071)',\n  \n  // Accent colors\n  '--accent': 'oklch(0.97 0.001 106.424)',\n  '--accent-foreground': 'oklch(0.216 0.006 56.043)',\n  \n  // Destructive colors\n  '--destructive': 'oklch(0.577 0.245 27.325)',\n  '--destructive-foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Border and input colors\n  '--border': 'oklch(0.923 0.003 48.717)',\n  '--input': 'oklch(0.923 0.003 48.717)',\n  '--ring': 'oklch(0.709 0.01 56.259)',\n  \n  // Chart colors\n  '--chart-1': 'oklch(0.646 0.222 41.116)',\n  '--chart-2': 'oklch(0.6 0.118 184.704)',\n  '--chart-3': 'oklch(0.398 0.07 227.392)',\n  '--chart-4': 'oklch(0.828 0.189 84.429)',\n  '--chart-5': 'oklch(0.769 0.188 70.08)',\n  \n  // Sidebar colors\n  '--sidebar': 'oklch(0.985 0.001 106.423)',\n  '--sidebar-foreground': 'oklch(0.147 0.004 49.25)',\n  '--sidebar-primary': 'oklch(0.216 0.006 56.043)',\n  '--sidebar-primary-foreground': 'oklch(0.985 0.001 106.423)',\n  '--sidebar-accent': 'oklch(0.97 0.001 106.424)',\n  '--sidebar-accent-foreground': 'oklch(0.216 0.006 56.043)',\n  '--sidebar-border': 'oklch(0.923 0.003 48.717)',\n  '--sidebar-ring': 'oklch(0.709 0.01 56.259)',\n  \n  // Border radius\n  '--radius': '0.625rem',\n} as const;\n\nexport const darkTheme = {\n  // Base colors\n  '--background': 'oklch(0.147 0.004 49.25)',\n  '--foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Card colors\n  '--card': 'oklch(0.216 0.006 56.043)',\n  '--card-foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Popover colors\n  '--popover': 'oklch(0.216 0.006 56.043)',\n  '--popover-foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Primary colors\n  '--primary': 'oklch(0.923 0.003 48.717)',\n  '--primary-foreground': 'oklch(0.216 0.006 56.043)',\n  \n  // Secondary colors\n  '--secondary': 'oklch(0.268 0.007 34.298)',\n  '--secondary-foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Muted colors\n  '--muted': 'oklch(0.268 0.007 34.298)',\n  '--muted-foreground': 'oklch(0.709 0.01 56.259)',\n  \n  // Accent colors\n  '--accent': 'oklch(0.268 0.007 34.298)',\n  '--accent-foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Destructive colors\n  '--destructive': 'oklch(0.704 0.191 22.216)',\n  '--destructive-foreground': 'oklch(0.985 0.001 106.423)',\n  \n  // Border and input colors\n  '--border': 'oklch(1 0 0 / 10%)',\n  '--input': 'oklch(1 0 0 / 15%)',\n  '--ring': 'oklch(0.553 0.013 58.071)',\n  \n  // Chart colors\n  '--chart-1': 'oklch(0.488 0.243 264.376)',\n  '--chart-2': 'oklch(0.696 0.17 162.48)',\n  '--chart-3': 'oklch(0.769 0.188 70.08)',\n  '--chart-4': 'oklch(0.627 0.265 303.9)',\n  '--chart-5': 'oklch(0.645 0.246 16.439)',\n  \n  // Sidebar colors\n  '--sidebar': 'oklch(0.216 0.006 56.043)',\n  '--sidebar-foreground': 'oklch(0.985 0.001 106.423)',\n  '--sidebar-primary': 'oklch(0.488 0.243 264.376)',\n  '--sidebar-primary-foreground': 'oklch(0.985 0.001 106.423)',\n  '--sidebar-accent': 'oklch(0.268 0.007 34.298)',\n  '--sidebar-accent-foreground': 'oklch(0.985 0.001 106.423)',\n  '--sidebar-border': 'oklch(1 0 0 / 10%)',\n  '--sidebar-ring': 'oklch(0.553 0.013 58.071)',\n  \n  // Border radius\n  '--radius': '0.625rem',\n} as const;\n\n// Theme configuration object\nexport const themeConfig = {\n  light: lightTheme,\n  dark: darkTheme,\n  defaultTheme: 'system' as ThemeMode,\n  enableSystem: true,\n  disableTransitionOnChange: false,\n  storageKey: 'loni-theme',\n  attribute: 'class',\n  defaultClass: 'light',\n  themes: ['light', 'dark'] as const,\n} as const;\n\n// Helper function to apply theme variables\nexport const applyThemeVariables = (theme: typeof lightTheme | typeof darkTheme) => {\n  if (typeof document === 'undefined') return;\n  \n  const root = document.documentElement;\n  Object.entries(theme).forEach(([property, value]) => {\n    root.style.setProperty(property, value);\n  });\n};\n\n// Helper function to get current theme variables\nexport const getCurrentThemeVariables = (mode: 'light' | 'dark') => {\n  return mode === 'light' ? lightTheme : darkTheme;\n};\n\n// CSS-in-JS theme object for styled-components or emotion (if needed)\nexport const styledTheme = {\n  light: {\n    colors: {\n      background: 'var(--background)',\n      foreground: 'var(--foreground)',\n      card: 'var(--card)',\n      cardForeground: 'var(--card-foreground)',\n      popover: 'var(--popover)',\n      popoverForeground: 'var(--popover-foreground)',\n      primary: 'var(--primary)',\n      primaryForeground: 'var(--primary-foreground)',\n      secondary: 'var(--secondary)',\n      secondaryForeground: 'var(--secondary-foreground)',\n      muted: 'var(--muted)',\n      mutedForeground: 'var(--muted-foreground)',\n      accent: 'var(--accent)',\n      accentForeground: 'var(--accent-foreground)',\n      destructive: 'var(--destructive)',\n      destructiveForeground: 'var(--destructive-foreground)',\n      border: 'var(--border)',\n      input: 'var(--input)',\n      ring: 'var(--ring)',\n    },\n    spacing: designSystem.spacing,\n    borderRadius: designSystem.borderRadius,\n    shadows: designSystem.shadows,\n    typography: designSystem.typography,\n    animations: designSystem.animations,\n    breakpoints: designSystem.breakpoints,\n    zIndex: designSystem.zIndex,\n  },\n  dark: {\n    colors: {\n      background: 'var(--background)',\n      foreground: 'var(--foreground)',\n      card: 'var(--card)',\n      cardForeground: 'var(--card-foreground)',\n      popover: 'var(--popover)',\n      popoverForeground: 'var(--popover-foreground)',\n      primary: 'var(--primary)',\n      primaryForeground: 'var(--primary-foreground)',\n      secondary: 'var(--secondary)',\n      secondaryForeground: 'var(--secondary-foreground)',\n      muted: 'var(--muted)',\n      mutedForeground: 'var(--muted-foreground)',\n      accent: 'var(--accent)',\n      accentForeground: 'var(--accent-foreground)',\n      destructive: 'var(--destructive)',\n      destructiveForeground: 'var(--destructive-foreground)',\n      border: 'var(--border)',\n      input: 'var(--input)',\n      ring: 'var(--ring)',\n    },\n    spacing: designSystem.spacing,\n    borderRadius: designSystem.borderRadius,\n    shadows: designSystem.shadows,\n    typography: designSystem.typography,\n    animations: designSystem.animations,\n    breakpoints: designSystem.breakpoints,\n    zIndex: designSystem.zIndex,\n  },\n} as const;\n\nexport type StyledTheme = typeof styledTheme.light;\nexport type ThemeConfig = typeof themeConfig;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAED;;AAMO,MAAM,aAAa;IACxB,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAEhB,cAAc;IACd,UAAU;IACV,qBAAqB;IAErB,iBAAiB;IACjB,aAAa;IACb,wBAAwB;IAExB,iBAAiB;IACjB,aAAa;IACb,wBAAwB;IAExB,mBAAmB;IACnB,eAAe;IACf,0BAA0B;IAE1B,eAAe;IACf,WAAW;IACX,sBAAsB;IAEtB,gBAAgB;IAChB,YAAY;IACZ,uBAAuB;IAEvB,qBAAqB;IACrB,iBAAiB;IACjB,4BAA4B;IAE5B,0BAA0B;IAC1B,YAAY;IACZ,WAAW;IACX,UAAU;IAEV,eAAe;IACf,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IAEb,iBAAiB;IACjB,aAAa;IACb,wBAAwB;IACxB,qBAAqB;IACrB,gCAAgC;IAChC,oBAAoB;IACpB,+BAA+B;IAC/B,oBAAoB;IACpB,kBAAkB;IAElB,gBAAgB;IAChB,YAAY;AACd;AAEO,MAAM,YAAY;IACvB,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAEhB,cAAc;IACd,UAAU;IACV,qBAAqB;IAErB,iBAAiB;IACjB,aAAa;IACb,wBAAwB;IAExB,iBAAiB;IACjB,aAAa;IACb,wBAAwB;IAExB,mBAAmB;IACnB,eAAe;IACf,0BAA0B;IAE1B,eAAe;IACf,WAAW;IACX,sBAAsB;IAEtB,gBAAgB;IAChB,YAAY;IACZ,uBAAuB;IAEvB,qBAAqB;IACrB,iBAAiB;IACjB,4BAA4B;IAE5B,0BAA0B;IAC1B,YAAY;IACZ,WAAW;IACX,UAAU;IAEV,eAAe;IACf,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IAEb,iBAAiB;IACjB,aAAa;IACb,wBAAwB;IACxB,qBAAqB;IACrB,gCAAgC;IAChC,oBAAoB;IACpB,+BAA+B;IAC/B,oBAAoB;IACpB,kBAAkB;IAElB,gBAAgB;IAChB,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,OAAO;IACP,MAAM;IACN,cAAc;IACd,cAAc;IACd,2BAA2B;IAC3B,YAAY;IACZ,WAAW;IACX,cAAc;IACd,QAAQ;QAAC;QAAS;KAAO;AAC3B;AAGO,MAAM,sBAAsB,CAAC;IAClC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,OAAO,SAAS,eAAe;IACrC,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;QAC9C,KAAK,KAAK,CAAC,WAAW,CAAC,UAAU;IACnC;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,OAAO,SAAS,UAAU,aAAa;AACzC;AAGO,MAAM,cAAc;IACzB,OAAO;QACL,QAAQ;YACN,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,gBAAgB;YAChB,SAAS;YACT,mBAAmB;YACnB,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,qBAAqB;YACrB,OAAO;YACP,iBAAiB;YACjB,QAAQ;YACR,kBAAkB;YAClB,aAAa;YACb,uBAAuB;YACvB,QAAQ;YACR,OAAO;YACP,MAAM;QACR;QACA,SAAS,8HAAA,CAAA,eAAY,CAAC,OAAO;QAC7B,cAAc,8HAAA,CAAA,eAAY,CAAC,YAAY;QACvC,SAAS,8HAAA,CAAA,eAAY,CAAC,OAAO;QAC7B,YAAY,8HAAA,CAAA,eAAY,CAAC,UAAU;QACnC,YAAY,8HAAA,CAAA,eAAY,CAAC,UAAU;QACnC,aAAa,8HAAA,CAAA,eAAY,CAAC,WAAW;QACrC,QAAQ,8HAAA,CAAA,eAAY,CAAC,MAAM;IAC7B;IACA,MAAM;QACJ,QAAQ;YACN,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,gBAAgB;YAChB,SAAS;YACT,mBAAmB;YACnB,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,qBAAqB;YACrB,OAAO;YACP,iBAAiB;YACjB,QAAQ;YACR,kBAAkB;YAClB,aAAa;YACb,uBAAuB;YACvB,QAAQ;YACR,OAAO;YACP,MAAM;QACR;QACA,SAAS,8HAAA,CAAA,eAAY,CAAC,OAAO;QAC7B,cAAc,8HAAA,CAAA,eAAY,CAAC,YAAY;QACvC,SAAS,8HAAA,CAAA,eAAY,CAAC,OAAO;QAC7B,YAAY,8HAAA,CAAA,eAAY,CAAC,UAAU;QACnC,YAAY,8HAAA,CAAA,eAAY,CAAC,UAAU;QACnC,aAAa,8HAAA,CAAA,eAAY,CAAC,WAAW;QACrC,QAAQ,8HAAA,CAAA,eAAY,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\nimport { themeConfig, applyThemeVariables, getCurrentThemeVariables } from \"@/lib/theme-config\"\nimport { designSystem } from \"@/lib/design-system\"\n\n// Design System Context\ninterface DesignSystemContextType {\n  designSystem: typeof designSystem;\n  theme: {\n    mode: string;\n    setTheme: (theme: string) => void;\n    resolvedTheme: string | undefined;\n    themes: string[];\n  };\n}\n\nconst DesignSystemContext = React.createContext<DesignSystemContextType | undefined>(undefined)\n\n// Custom hook to use design system\nexport function useDesignSystem() {\n  const context = React.useContext(DesignSystemContext)\n  if (context === undefined) {\n    throw new Error(\"useDesignSystem must be used within a DesignSystemProvider\")\n  }\n  return context\n}\n\n// Custom hook to use theme\nexport function useTheme() {\n  const context = React.useContext(DesignSystemContext)\n  if (context === undefined) {\n    throw new Error(\"useTheme must be used within a DesignSystemProvider\")\n  }\n  return context.theme\n}\n\n// Theme Provider Component\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return (\n    <NextThemesProvider\n      attribute={themeConfig.attribute}\n      defaultTheme={themeConfig.defaultTheme}\n      enableSystem={themeConfig.enableSystem}\n      disableTransitionOnChange={themeConfig.disableTransitionOnChange}\n      storageKey={themeConfig.storageKey}\n      themes={[...themeConfig.themes]}\n      {...props}\n    >\n      {children}\n    </NextThemesProvider>\n  )\n}\n\n// Design System Provider Component\nexport function DesignSystemProvider({ \n  children,\n  ...themeProps \n}: ThemeProviderProps) {\n  const [mounted, setMounted] = React.useState(false)\n\n  // Ensure component is mounted before accessing theme\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  return (\n    <NextThemesProvider\n      attribute={themeConfig.attribute}\n      defaultTheme={themeConfig.defaultTheme}\n      enableSystem={themeConfig.enableSystem}\n      disableTransitionOnChange={themeConfig.disableTransitionOnChange}\n      storageKey={themeConfig.storageKey}\n      themes={[...themeConfig.themes]}\n      {...themeProps}\n    >\n      <DesignSystemProviderInner mounted={mounted}>\n        {children}\n      </DesignSystemProviderInner>\n    </NextThemesProvider>\n  )\n}\n\n// Inner provider component that has access to theme context\nfunction DesignSystemProviderInner({ \n  children, \n  mounted \n}: { \n  children: React.ReactNode;\n  mounted: boolean;\n}) {\n  const { theme, setTheme, resolvedTheme, themes } = useThemeFromNextThemes()\n\n  // Apply theme variables when theme changes\n  React.useEffect(() => {\n    if (!mounted || !resolvedTheme) return\n\n    const themeVariables = getCurrentThemeVariables(\n      resolvedTheme as 'light' | 'dark'\n    )\n    applyThemeVariables(themeVariables)\n  }, [resolvedTheme, mounted])\n\n  const contextValue: DesignSystemContextType = {\n    designSystem,\n    theme: {\n      mode: theme,\n      setTheme,\n      resolvedTheme,\n      themes,\n    },\n  }\n\n  return (\n    <DesignSystemContext.Provider value={contextValue}>\n      {children}\n    </DesignSystemContext.Provider>\n  )\n}\n\n// Helper hook to get theme from next-themes\nfunction useThemeFromNextThemes() {\n  try {\n    // Dynamic import to avoid SSR issues\n    const { useTheme } = require(\"next-themes\")\n    return useTheme()\n  } catch {\n    // Fallback if next-themes is not available\n    return {\n      theme: 'light',\n      setTheme: () => {},\n      resolvedTheme: 'light',\n      themes: ['light', 'dark'],\n    }\n  }\n}\n\n// Utility component for theme-aware styling\nexport function ThemeAwareComponent({ \n  children,\n  lightClassName = \"\",\n  darkClassName = \"\",\n  className = \"\",\n}: {\n  children: React.ReactNode;\n  lightClassName?: string;\n  darkClassName?: string;\n  className?: string;\n}) {\n  const { theme } = useTheme()\n  \n  const themeSpecificClass = theme === 'dark' ? darkClassName : lightClassName\n  const combinedClassName = `${className} ${themeSpecificClass}`.trim()\n\n  return (\n    <div className={combinedClassName}>\n      {children}\n    </div>\n  )\n}\n\n// Hook for responsive design\nexport function useResponsive() {\n  const [windowSize, setWindowSize] = React.useState({\n    width: 0,\n    height: 0,\n  })\n\n  React.useEffect(() => {\n    function handleResize() {\n      setWindowSize({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      })\n    }\n\n    // Set initial size\n    handleResize()\n\n    window.addEventListener('resize', handleResize)\n    return () => window.removeEventListener('resize', handleResize)\n  }, [])\n\n  const breakpoints = designSystem.breakpoints\n  \n  return {\n    windowSize,\n    isMobile: windowSize.width < parseInt(breakpoints.sm),\n    isTablet: windowSize.width >= parseInt(breakpoints.sm) && windowSize.width < parseInt(breakpoints.lg),\n    isDesktop: windowSize.width >= parseInt(breakpoints.lg),\n    isLarge: windowSize.width >= parseInt(breakpoints.xl),\n    isXLarge: windowSize.width >= parseInt(breakpoints['2xl']),\n  }\n}\n\n// Hook for accessing design tokens\nexport function useDesignTokens() {\n  const { designSystem } = useDesignSystem()\n  \n  return {\n    colors: designSystem.colors,\n    typography: designSystem.typography,\n    spacing: designSystem.spacing,\n    borderRadius: designSystem.borderRadius,\n    shadows: designSystem.shadows,\n    animations: designSystem.animations,\n    breakpoints: designSystem.breakpoints,\n    zIndex: designSystem.zIndex,\n    componentVariants: designSystem.componentVariants,\n  }\n}\n\n// Export types\nexport type { DesignSystemContextType, ThemeProviderProps }\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAmBA,MAAM,oCAAsB,qMAAA,CAAA,gBAAmB,CAAsC;AAG9E,SAAS;IACd,MAAM,UAAU,qMAAA,CAAA,aAAgB,CAAC;IACjC,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,UAAU,qMAAA,CAAA,aAAgB,CAAC;IACjC,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,QAAQ,KAAK;AACtB;AAGO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBACE,8OAAC,gJAAA,CAAA,gBAAkB;QACjB,WAAW,6HAAA,CAAA,cAAW,CAAC,SAAS;QAChC,cAAc,6HAAA,CAAA,cAAW,CAAC,YAAY;QACtC,cAAc,6HAAA,CAAA,cAAW,CAAC,YAAY;QACtC,2BAA2B,6HAAA,CAAA,cAAW,CAAC,yBAAyB;QAChE,YAAY,6HAAA,CAAA,cAAW,CAAC,UAAU;QAClC,QAAQ;eAAI,6HAAA,CAAA,cAAW,CAAC,MAAM;SAAC;QAC9B,GAAG,KAAK;kBAER;;;;;;AAGP;AAGO,SAAS,qBAAqB,EACnC,QAAQ,EACR,GAAG,YACgB;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,WAAc,CAAC;IAE7C,qDAAqD;IACrD,qMAAA,CAAA,YAAe,CAAC;QACd,WAAW;IACb,GAAG,EAAE;IAEL,qBACE,8OAAC,gJAAA,CAAA,gBAAkB;QACjB,WAAW,6HAAA,CAAA,cAAW,CAAC,SAAS;QAChC,cAAc,6HAAA,CAAA,cAAW,CAAC,YAAY;QACtC,cAAc,6HAAA,CAAA,cAAW,CAAC,YAAY;QACtC,2BAA2B,6HAAA,CAAA,cAAW,CAAC,yBAAyB;QAChE,YAAY,6HAAA,CAAA,cAAW,CAAC,UAAU;QAClC,QAAQ;eAAI,6HAAA,CAAA,cAAW,CAAC,MAAM;SAAC;QAC9B,GAAG,UAAU;kBAEd,cAAA,8OAAC;YAA0B,SAAS;sBACjC;;;;;;;;;;;AAIT;AAEA,4DAA4D;AAC5D,SAAS,0BAA0B,EACjC,QAAQ,EACR,OAAO,EAIR;IACC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG;IAEnD,2CAA2C;IAC3C,qMAAA,CAAA,YAAe,CAAC;QACd,IAAI,CAAC,WAAW,CAAC,eAAe;QAEhC,MAAM,iBAAiB,CAAA,GAAA,6HAAA,CAAA,2BAAwB,AAAD,EAC5C;QAEF,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE;IACtB,GAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,eAAwC;QAC5C,cAAA,8HAAA,CAAA,eAAY;QACZ,OAAO;YACL,MAAM;YACN;YACA;YACA;QACF;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;kBAClC;;;;;;AAGP;AAEA,4CAA4C;AAC5C,SAAS;IACP,IAAI;QACF,qCAAqC;QACrC,MAAM,EAAE,QAAQ,EAAE;QAClB,OAAO;IACT,EAAE,OAAM;QACN,2CAA2C;QAC3C,OAAO;YACL,OAAO;YACP,UAAU,KAAO;YACjB,eAAe;YACf,QAAQ;gBAAC;gBAAS;aAAO;QAC3B;IACF;AACF;AAGO,SAAS,oBAAoB,EAClC,QAAQ,EACR,iBAAiB,EAAE,EACnB,gBAAgB,EAAE,EAClB,YAAY,EAAE,EAMf;IACC,MAAM,EAAE,KAAK,EAAE,GAAG;IAElB,MAAM,qBAAqB,UAAU,SAAS,gBAAgB;IAC9D,MAAM,oBAAoB,GAAG,UAAU,CAAC,EAAE,oBAAoB,CAAC,IAAI;IAEnE,qBACE,8OAAC;QAAI,WAAW;kBACb;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,WAAc,CAAC;QACjD,OAAO;QACP,QAAQ;IACV;IAEA,qMAAA,CAAA,YAAe,CAAC;QACd,SAAS;YACP,cAAc;gBACZ,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;QACF;QAEA,mBAAmB;QACnB;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc,8HAAA,CAAA,eAAY,CAAC,WAAW;IAE5C,OAAO;QACL;QACA,UAAU,WAAW,KAAK,GAAG,SAAS,YAAY,EAAE;QACpD,UAAU,WAAW,KAAK,IAAI,SAAS,YAAY,EAAE,KAAK,WAAW,KAAK,GAAG,SAAS,YAAY,EAAE;QACpG,WAAW,WAAW,KAAK,IAAI,SAAS,YAAY,EAAE;QACtD,SAAS,WAAW,KAAK,IAAI,SAAS,YAAY,EAAE;QACpD,UAAU,WAAW,KAAK,IAAI,SAAS,WAAW,CAAC,MAAM;IAC3D;AACF;AAGO,SAAS;IACd,MAAM,EAAE,YAAY,EAAE,GAAG;IAEzB,OAAO;QACL,QAAQ,aAAa,MAAM;QAC3B,YAAY,aAAa,UAAU;QACnC,SAAS,aAAa,OAAO;QAC7B,cAAc,aAAa,YAAY;QACvC,SAAS,aAAa,OAAO;QAC7B,YAAY,aAAa,UAAU;QACnC,aAAa,aAAa,WAAW;QACrC,QAAQ,aAAa,MAAM;QAC3B,mBAAmB,aAAa,iBAAiB;IACnD;AACF", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/layout/navbar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { useTheme } from \"@/components/providers/theme-provider\"\nimport { Moon, Sun, Settings, User, Bell, Search } from \"lucide-react\"\nimport { Input } from \"@/components/ui/input\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Badge } from \"@/components/ui/badge\"\n\nexport function Navbar() {\n  const { resolvedTheme, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')\n  }\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"flex h-full items-center justify-between px-6\">\n        {/* Left Section - Logo and Title */}\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"h-8 w-8 rounded-lg bg-primary flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold text-sm\">L</span>\n            </div>\n            <span className=\"font-bold text-lg\">Loni</span>\n          </div>\n        </div>\n\n        {/* Center Section - Search */}\n        <div className=\"flex-1 max-w-md mx-8\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search...\"\n              className=\"pl-10 w-full\"\n            />\n          </div>\n        </div>\n\n        {/* Right Section - Actions and User Menu */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Theme Toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={toggleTheme}\n            className=\"h-9 w-9\"\n          >\n            <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n            <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n            <span className=\"sr-only\">Toggle theme</span>\n          </Button>\n\n          {/* Notifications */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"h-9 w-9 relative\">\n            <Bell className=\"h-4 w-4\" />\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n            >\n              3\n            </Badge>\n            <span className=\"sr-only\">Notifications</span>\n          </Button>\n\n          {/* Settings */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"h-9 w-9\">\n            <Settings className=\"h-4 w-4\" />\n            <span className=\"sr-only\">Settings</span>\n          </Button>\n\n          {/* User Menu */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"icon\" className=\"h-9 w-9\">\n                <User className=\"h-4 w-4\" />\n                <span className=\"sr-only\">User menu</span>\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"w-56\">\n              <DropdownMenuLabel>My Account</DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem>\n                <User className=\"mr-2 h-4 w-4\" />\n                Profile\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <Settings className=\"mr-2 h-4 w-4\" />\n                Settings\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem>\n                Log out\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEA;;;;;;AANA;;;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD;IAE3C,MAAM,cAAc;QAClB,SAAS,kBAAkB,SAAS,UAAU;IAChD;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;0CAE9D,8OAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;;;;;;8BAKxC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;;8CAC5C,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCACC,SAAQ;oCACR,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;;8CAC5C,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,WAAU;;0DAC5C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;8CAG9B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,oBAAiB;sDAAC;;;;;;sDACnB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShC", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 1450, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { cn } from \"@/lib/utils\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Badge } from \"@/components/ui/badge\"\nimport {\n  Home,\n  Palette,\n  Settings,\n  Users,\n  BarChart3,\n  FileText,\n  Image,\n  Layout,\n  Layers,\n  Zap,\n  ChevronRight,\n  Folder,\n  FolderOpen\n} from \"lucide-react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { useState } from \"react\"\n\ninterface SidebarItem {\n  title: string\n  href?: string\n  icon: React.ComponentType<{ className?: string }>\n  badge?: string\n  children?: SidebarItem[]\n}\n\nconst sidebarItems: SidebarItem[] = [\n  {\n    title: \"Dashboard\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    title: \"Style Configuration\",\n    href: \"/style-config\",\n    icon: Palette,\n    badge: \"New\"\n  },\n  {\n    title: \"Components\",\n    icon: Layout,\n    children: [\n      {\n        title: \"UI Components\",\n        href: \"/components/ui\",\n        icon: Layers,\n      },\n      {\n        title: \"Forms\",\n        href: \"/components/forms\",\n        icon: FileText,\n      },\n      {\n        title: \"Data Display\",\n        href: \"/components/data\",\n        icon: BarChart3,\n      },\n    ]\n  },\n  {\n    title: \"Design System\",\n    icon: Zap,\n    children: [\n      {\n        title: \"Typography\",\n        href: \"/design/typography\",\n        icon: FileText,\n      },\n      {\n        title: \"Colors\",\n        href: \"/design/colors\",\n        icon: Palette,\n      },\n      {\n        title: \"Spacing\",\n        href: \"/design/spacing\",\n        icon: Layout,\n      },\n    ]\n  },\n  {\n    title: \"Media\",\n    href: \"/media\",\n    icon: Image,\n  },\n  {\n    title: \"Users\",\n    href: \"/users\",\n    icon: Users,\n  },\n  {\n    title: \"Analytics\",\n    href: \"/analytics\",\n    icon: BarChart3,\n  },\n  {\n    title: \"Settings\",\n    href: \"/settings\",\n    icon: Settings,\n  },\n]\n\ninterface SidebarItemProps {\n  item: SidebarItem\n  level?: number\n}\n\nfunction SidebarItemComponent({ item, level = 0 }: SidebarItemProps) {\n  const pathname = usePathname()\n  const [isOpen, setIsOpen] = useState(false)\n  const hasChildren = item.children && item.children.length > 0\n  const isActive = item.href === pathname\n  const isChildActive = item.children?.some(child => child.href === pathname)\n\n  const handleClick = () => {\n    if (hasChildren) {\n      setIsOpen(!isOpen)\n    }\n  }\n\n  const ItemContent = (\n    <div\n      className={cn(\n        \"flex items-center justify-between w-full px-3 py-2 text-sm rounded-md transition-colors\",\n        \"hover:bg-accent hover:text-accent-foreground\",\n        isActive && \"bg-accent text-accent-foreground\",\n        isChildActive && \"bg-accent/50\",\n        level > 0 && \"ml-4\"\n      )}\n    >\n      <div className=\"flex items-center space-x-3\">\n        <item.icon className=\"h-4 w-4\" />\n        <span>{item.title}</span>\n        {item.badge && (\n          <Badge variant=\"secondary\" className=\"text-xs\">\n            {item.badge}\n          </Badge>\n        )}\n      </div>\n      {hasChildren && (\n        <ChevronRight\n          className={cn(\n            \"h-4 w-4 transition-transform\",\n            isOpen && \"rotate-90\"\n          )}\n        />\n      )}\n    </div>\n  )\n\n  return (\n    <div>\n      {item.href ? (\n        <Link href={item.href}>\n          {ItemContent}\n        </Link>\n      ) : (\n        <button onClick={handleClick} className=\"w-full text-left\">\n          {ItemContent}\n        </button>\n      )}\n      \n      {hasChildren && isOpen && (\n        <div className=\"mt-1 space-y-1\">\n          {item.children?.map((child, index) => (\n            <SidebarItemComponent\n              key={index}\n              item={child}\n              level={level + 1}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport function Sidebar() {\n  return (\n    <div className=\"fixed left-0 top-16 bottom-0 w-64 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <ScrollArea className=\"h-full py-6 px-4\">\n        <div className=\"space-y-2\">\n          <div className=\"px-3 py-2\">\n            <h2 className=\"mb-2 px-0 text-lg font-semibold tracking-tight\">\n              Navigation\n            </h2>\n          </div>\n          <Separator className=\"my-4\" />\n          <div className=\"space-y-1\">\n            {sidebarItems.map((item, index) => (\n              <SidebarItemComponent key={index} item={item} />\n            ))}\n          </div>\n        </div>\n      </ScrollArea>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AAxBA;;;;;;;;;;AAkCA,MAAM,eAA8B;IAClC;QACE,OAAO;QACP,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM,qNAAA,CAAA,SAAM;QACZ,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,sMAAA,CAAA,SAAM;YACd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,8MAAA,CAAA,WAAQ;YAChB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,kNAAA,CAAA,YAAS;YACjB;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM,gMAAA,CAAA,MAAG;QACT,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,8MAAA,CAAA,WAAQ;YAChB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wMAAA,CAAA,UAAO;YACf;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,qNAAA,CAAA,SAAM;YACd;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;IACjB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;CACD;AAOD,SAAS,qBAAqB,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAoB;IACjE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;IAC5D,MAAM,WAAW,KAAK,IAAI,KAAK;IAC/B,MAAM,gBAAgB,KAAK,QAAQ,EAAE,KAAK,CAAA,QAAS,MAAM,IAAI,KAAK;IAElE,MAAM,cAAc;QAClB,IAAI,aAAa;YACf,UAAU,CAAC;QACb;IACF;IAEA,MAAM,4BACJ,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,gDACA,YAAY,oCACZ,iBAAiB,gBACjB,QAAQ,KAAK;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,KAAK,IAAI;wBAAC,WAAU;;;;;;kCACrB,8OAAC;kCAAM,KAAK,KAAK;;;;;;oBAChB,KAAK,KAAK,kBACT,8OAAC;wBAAM,SAAQ;wBAAY,WAAU;kCAClC,KAAK,KAAK;;;;;;;;;;;;YAIhB,6BACC,8OAAC,sNAAA,CAAA,eAAY;gBACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gCACA,UAAU;;;;;;;;;;;;IAOpB,qBACE,8OAAC;;YACE,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,KAAK,IAAI;0BAClB;;;;;qCAGH,8OAAC;gBAAO,SAAS;gBAAa,WAAU;0BACrC;;;;;;YAIJ,eAAe,wBACd,8OAAC;gBAAI,WAAU;0BACZ,KAAK,QAAQ,EAAE,IAAI,CAAC,OAAO,sBAC1B,8OAAC;wBAEC,MAAM;wBACN,OAAO,QAAQ;uBAFV;;;;;;;;;;;;;;;;AASnB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;YAAC,WAAU;sBACpB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;;;;;;kCAIjE,8OAAC,qIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;gCAAiC,MAAM;+BAAb;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 1777, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/layout/app-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Navbar } from \"./navbar\"\nimport { Sidebar } from \"./sidebar\"\nimport { cn } from \"@/lib/utils\"\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function AppLayout({ children, className }: AppLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Fixed Navbar */}\n      <Navbar />\n      \n      {/* Fixed Sidebar */}\n      <Sidebar />\n      \n      {/* Main Content Area */}\n      <main \n        className={cn(\n          \"ml-64 mt-16 min-h-[calc(100vh-4rem)]\",\n          \"transition-all duration-300 ease-in-out\",\n          className\n        )}\n      >\n        <div className=\"container mx-auto p-6\">\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAkB;IAC/D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAGP,8OAAC,uIAAA,CAAA,UAAO;;;;;0BAGR,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA,2CACA;0BAGF,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Projects/lonors/loni/apps/frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}]}