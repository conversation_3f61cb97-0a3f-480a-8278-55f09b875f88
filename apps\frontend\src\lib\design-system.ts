/**
 * Centralized Design System Configuration
 * All design tokens, theme configuration, and styling constants
 */

// Typography Configuration
export const typography = {
  fontFamilies: {
    sans: ['var(--font-geist-sans)', 'system-ui', 'sans-serif'],
    mono: ['var(--font-geist-mono)', 'Consolas', 'monospace'],
  },
  fontSizes: {
    xs: '0.75rem',     // 12px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
    '7xl': '4.5rem',   // 72px
    '8xl': '6rem',     // 96px
    '9xl': '8rem',     // 128px
  },
  fontWeights: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },
  lineHeights: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
} as const;

// Color Palette Configuration
export const colors = {
  // Primary brand colors
  primary: {
    50: 'oklch(0.985 0.001 106.423)',
    100: 'oklch(0.97 0.001 106.424)',
    200: 'oklch(0.923 0.003 48.717)',
    300: 'oklch(0.709 0.01 56.259)',
    400: 'oklch(0.553 0.013 58.071)',
    500: 'oklch(0.216 0.006 56.043)',
    600: 'oklch(0.147 0.004 49.25)',
    700: 'oklch(0.147 0.004 49.25)',
    800: 'oklch(0.147 0.004 49.25)',
    900: 'oklch(0.147 0.004 49.25)',
    950: 'oklch(0.147 0.004 49.25)',
  },
  // Secondary colors
  secondary: {
    50: 'oklch(0.985 0.001 106.423)',
    100: 'oklch(0.97 0.001 106.424)',
    200: 'oklch(0.923 0.003 48.717)',
    300: 'oklch(0.709 0.01 56.259)',
    400: 'oklch(0.553 0.013 58.071)',
    500: 'oklch(0.268 0.007 34.298)',
    600: 'oklch(0.216 0.006 56.043)',
    700: 'oklch(0.147 0.004 49.25)',
    800: 'oklch(0.147 0.004 49.25)',
    900: 'oklch(0.147 0.004 49.25)',
    950: 'oklch(0.147 0.004 49.25)',
  },
  // Accent colors
  accent: {
    50: 'oklch(0.985 0.001 106.423)',
    100: 'oklch(0.97 0.001 106.424)',
    200: 'oklch(0.923 0.003 48.717)',
    300: 'oklch(0.709 0.01 56.259)',
    400: 'oklch(0.553 0.013 58.071)',
    500: 'oklch(0.268 0.007 34.298)',
    600: 'oklch(0.216 0.006 56.043)',
    700: 'oklch(0.147 0.004 49.25)',
    800: 'oklch(0.147 0.004 49.25)',
    900: 'oklch(0.147 0.004 49.25)',
    950: 'oklch(0.147 0.004 49.25)',
  },
  // Neutral colors
  neutral: {
    50: 'oklch(0.985 0.001 106.423)',
    100: 'oklch(0.97 0.001 106.424)',
    200: 'oklch(0.923 0.003 48.717)',
    300: 'oklch(0.709 0.01 56.259)',
    400: 'oklch(0.553 0.013 58.071)',
    500: 'oklch(0.268 0.007 34.298)',
    600: 'oklch(0.216 0.006 56.043)',
    700: 'oklch(0.147 0.004 49.25)',
    800: 'oklch(0.147 0.004 49.25)',
    900: 'oklch(0.147 0.004 49.25)',
    950: 'oklch(0.147 0.004 49.25)',
  },
  // Semantic colors
  success: {
    50: 'oklch(0.95 0.05 142)',
    100: 'oklch(0.9 0.1 142)',
    200: 'oklch(0.85 0.15 142)',
    300: 'oklch(0.8 0.2 142)',
    400: 'oklch(0.75 0.25 142)',
    500: 'oklch(0.7 0.3 142)',
    600: 'oklch(0.65 0.35 142)',
    700: 'oklch(0.6 0.4 142)',
    800: 'oklch(0.55 0.45 142)',
    900: 'oklch(0.5 0.5 142)',
    950: 'oklch(0.45 0.55 142)',
  },
  warning: {
    50: 'oklch(0.95 0.05 85)',
    100: 'oklch(0.9 0.1 85)',
    200: 'oklch(0.85 0.15 85)',
    300: 'oklch(0.8 0.2 85)',
    400: 'oklch(0.75 0.25 85)',
    500: 'oklch(0.7 0.3 85)',
    600: 'oklch(0.65 0.35 85)',
    700: 'oklch(0.6 0.4 85)',
    800: 'oklch(0.55 0.45 85)',
    900: 'oklch(0.5 0.5 85)',
    950: 'oklch(0.45 0.55 85)',
  },
  error: {
    50: 'oklch(0.95 0.05 27)',
    100: 'oklch(0.9 0.1 27)',
    200: 'oklch(0.85 0.15 27)',
    300: 'oklch(0.8 0.2 27)',
    400: 'oklch(0.75 0.25 27)',
    500: 'oklch(0.577 0.245 27.325)',
    600: 'oklch(0.65 0.35 27)',
    700: 'oklch(0.6 0.4 27)',
    800: 'oklch(0.55 0.45 27)',
    900: 'oklch(0.5 0.5 27)',
    950: 'oklch(0.45 0.55 27)',
  },
} as const;

// Spacing Scale Configuration
export const spacing = {
  px: '1px',
  0: '0px',
  0.5: '0.125rem',   // 2px
  1: '0.25rem',      // 4px
  1.5: '0.375rem',   // 6px
  2: '0.5rem',       // 8px
  2.5: '0.625rem',   // 10px
  3: '0.75rem',      // 12px
  3.5: '0.875rem',   // 14px
  4: '1rem',         // 16px
  5: '1.25rem',      // 20px
  6: '1.5rem',       // 24px
  7: '1.75rem',      // 28px
  8: '2rem',         // 32px
  9: '2.25rem',      // 36px
  10: '2.5rem',      // 40px
  11: '2.75rem',     // 44px
  12: '3rem',        // 48px
  14: '3.5rem',      // 56px
  16: '4rem',        // 64px
  20: '5rem',        // 80px
  24: '6rem',        // 96px
  28: '7rem',        // 112px
  32: '8rem',        // 128px
  36: '9rem',        // 144px
  40: '10rem',       // 160px
  44: '11rem',       // 176px
  48: '12rem',       // 192px
  52: '13rem',       // 208px
  56: '14rem',       // 224px
  60: '15rem',       // 240px
  64: '16rem',       // 256px
  72: '18rem',       // 288px
  80: '20rem',       // 320px
  96: '24rem',       // 384px
} as const;

// Border Radius Configuration
export const borderRadius = {
  none: '0px',
  sm: 'calc(var(--radius) - 4px)',
  md: 'calc(var(--radius) - 2px)',
  lg: 'var(--radius)',
  xl: 'calc(var(--radius) + 4px)',
  '2xl': 'calc(var(--radius) + 8px)',
  '3xl': 'calc(var(--radius) + 12px)',
  full: '9999px',
} as const;

// Shadow Configuration
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  none: '0 0 #0000',
} as const;

// Animation Configuration
export const animations = {
  durations: {
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },
  timingFunctions: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
} as const;

// Breakpoints Configuration
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Z-Index Configuration
export const zIndex = {
  auto: 'auto',
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modal: '1040',
  popover: '1050',
  tooltip: '1060',
  toast: '1070',
} as const;

// Component Variants Configuration
export const componentVariants = {
  button: {
    sizes: {
      sm: 'h-8 px-3 text-xs',
      md: 'h-9 px-4 py-2',
      lg: 'h-10 px-8',
      xl: 'h-11 px-8',
      icon: 'h-9 w-9',
    },
    variants: {
      default: 'bg-primary text-primary-foreground shadow hover:bg-primary/90',
      destructive: 'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
      outline: 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
      secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'text-primary underline-offset-4 hover:underline',
    },
  },
  input: {
    sizes: {
      sm: 'h-8 px-3 text-xs',
      md: 'h-9 px-3',
      lg: 'h-10 px-3',
    },
  },
  card: {
    variants: {
      default: 'border bg-card text-card-foreground shadow',
      elevated: 'border bg-card text-card-foreground shadow-lg',
      outline: 'border-2 bg-card text-card-foreground',
    },
  },
} as const;

// Export the complete design system
export const designSystem = {
  typography,
  colors,
  spacing,
  borderRadius,
  shadows,
  animations,
  breakpoints,
  zIndex,
  componentVariants,
} as const;

export type DesignSystem = typeof designSystem;
export type Typography = typeof typography;
export type Colors = typeof colors;
export type Spacing = typeof spacing;
export type BorderRadius = typeof borderRadius;
export type Shadows = typeof shadows;
export type Animations = typeof animations;
export type Breakpoints = typeof breakpoints;
export type ZIndex = typeof zIndex;
export type ComponentVariants = typeof componentVariants;
