"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useTheme } from "@/components/providers/theme-provider"
import { useStyleConfig, type StyleConfig } from "@/lib/style-config-manager"
import { 
  Palette, 
  Type, 
  Layout, 
  Zap, 
  Save, 
  RotateCc<PERSON>, 
  Download, 
  Upload,
  Eye,
  EyeOff,
  Paintbrush,
  Setting<PERSON>
} from "lucide-react"
import { toast } from "sonner"

export default function StyleConfigPage() {
  const { theme, setTheme } = useTheme()
  const { config, updateConfig: updateStyleConfig, resetConfig: resetStyleConfig, exportConfig: exportStyleConfig, importConfig: importStyleConfig } = useStyleConfig()
  const [previewMode, setPreviewMode] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Track changes by comparing with saved config
  useEffect(() => {
    const savedConfig = localStorage.getItem('loni-style-config')
    const currentConfigString = JSON.stringify(config)
    const savedConfigString = savedConfig || JSON.stringify(config)
    setHasChanges(currentConfigString !== savedConfigString)
  }, [config])

  const updateConfig = (path: string, value: any) => {
    const newConfig = { ...config }
    const keys = path.split('.')
    let current = newConfig as any

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]]
    }

    current[keys[keys.length - 1]] = value
    updateStyleConfig(newConfig)
  }

  const saveConfig = () => {
    try {
      setHasChanges(false)
      toast.success('Style configuration saved successfully!')

      // Apply theme changes immediately
      if (config.theme.mode !== 'system') {
        setTheme(config.theme.mode)
      }
    } catch (error) {
      toast.error('Failed to save configuration')
      console.error('Save error:', error)
    }
  }

  const resetConfig = () => {
    resetStyleConfig()
    toast.info('Configuration reset to defaults')
  }

  const exportConfig = () => {
    const dataStr = exportStyleConfig()
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'loni-style-config.json'
    link.click()
    URL.revokeObjectURL(url)
    toast.success('Configuration exported!')
  }

  const importConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const configString = e.target?.result as string
        if (importStyleConfig(configString)) {
          toast.success('Configuration imported successfully!')
        } else {
          toast.error('Failed to import configuration')
        }
      } catch (error) {
        toast.error('Failed to import configuration')
        console.error('Import error:', error)
      }
    }
    reader.readAsText(file)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Style Configuration</h1>
          <p className="text-muted-foreground">
            Customize the design system tokens and theme settings
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPreviewMode(!previewMode)}
          >
            {previewMode ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
            {previewMode ? 'Exit Preview' : 'Preview'}
          </Button>
          
          <Button variant="outline" size="sm" onClick={resetConfig}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          
          <Button variant="outline" size="sm" onClick={exportConfig}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          
          <div className="relative">
            <input
              type="file"
              accept=".json"
              onChange={importConfig}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
          </div>
          
          <Button 
            onClick={saveConfig} 
            disabled={!hasChanges}
            className="relative"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
            {hasChanges && (
              <Badge variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0">
                !
              </Badge>
            )}
          </Button>
        </div>
      </div>

      {/* Configuration Tabs */}
      <Tabs defaultValue="theme" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="theme" className="flex items-center space-x-2">
            <Palette className="h-4 w-4" />
            <span>Theme</span>
          </TabsTrigger>
          <TabsTrigger value="typography" className="flex items-center space-x-2">
            <Type className="h-4 w-4" />
            <span>Typography</span>
          </TabsTrigger>
          <TabsTrigger value="spacing" className="flex items-center space-x-2">
            <Layout className="h-4 w-4" />
            <span>Spacing</span>
          </TabsTrigger>
          <TabsTrigger value="components" className="flex items-center space-x-2">
            <Paintbrush className="h-4 w-4" />
            <span>Components</span>
          </TabsTrigger>
          <TabsTrigger value="animations" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Animations</span>
          </TabsTrigger>
        </TabsList>

        {/* Theme Configuration */}
        <TabsContent value="theme" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Theme Mode</CardTitle>
                <CardDescription>
                  Choose between light, dark, or system preference
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Theme Mode</Label>
                  <Select
                    value={config.theme.mode}
                    onValueChange={(value: 'light' | 'dark' | 'system') =>
                      updateConfig('theme.mode', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Color Palette</CardTitle>
                <CardDescription>
                  Customize the primary color scheme
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Primary Color</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="color"
                        value={config.theme.primaryColor}
                        onChange={(e) => updateConfig('theme.primaryColor', e.target.value)}
                        className="w-12 h-10 p-1 border rounded"
                      />
                      <Input
                        value={config.theme.primaryColor}
                        onChange={(e) => updateConfig('theme.primaryColor', e.target.value)}
                        placeholder="#000000"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Secondary Color</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="color"
                        value={config.theme.secondaryColor}
                        onChange={(e) => updateConfig('theme.secondaryColor', e.target.value)}
                        className="w-12 h-10 p-1 border rounded"
                      />
                      <Input
                        value={config.theme.secondaryColor}
                        onChange={(e) => updateConfig('theme.secondaryColor', e.target.value)}
                        placeholder="#000000"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Accent Color</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="color"
                        value={config.theme.accentColor}
                        onChange={(e) => updateConfig('theme.accentColor', e.target.value)}
                        className="w-12 h-10 p-1 border rounded"
                      />
                      <Input
                        value={config.theme.accentColor}
                        onChange={(e) => updateConfig('theme.accentColor', e.target.value)}
                        placeholder="#000000"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Background Color</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="color"
                        value={config.theme.backgroundColor}
                        onChange={(e) => updateConfig('theme.backgroundColor', e.target.value)}
                        className="w-12 h-10 p-1 border rounded"
                      />
                      <Input
                        value={config.theme.backgroundColor}
                        onChange={(e) => updateConfig('theme.backgroundColor', e.target.value)}
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Typography Configuration */}
        <TabsContent value="typography" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Font Settings</CardTitle>
                <CardDescription>
                  Configure font family and base sizing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Font Family</Label>
                  <Select
                    value={config.typography.fontFamily}
                    onValueChange={(value) => updateConfig('typography.fontFamily', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Inter">Inter</SelectItem>
                      <SelectItem value="Roboto">Roboto</SelectItem>
                      <SelectItem value="Open Sans">Open Sans</SelectItem>
                      <SelectItem value="Lato">Lato</SelectItem>
                      <SelectItem value="Poppins">Poppins</SelectItem>
                      <SelectItem value="Montserrat">Montserrat</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Base Font Size: {config.typography.fontSize.base}px</Label>
                  <Slider
                    value={[config.typography.fontSize.base]}
                    onValueChange={([value]) => updateConfig('typography.fontSize.base', value)}
                    min={12}
                    max={24}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Type Scale: {config.typography.fontSize.scale}</Label>
                  <Slider
                    value={[config.typography.fontSize.scale]}
                    onValueChange={([value]) => updateConfig('typography.fontSize.scale', value)}
                    min={1.067}
                    max={1.618}
                    step={0.001}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Font Weights</CardTitle>
                <CardDescription>
                  Set font weights for different text elements
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Normal: {config.typography.fontWeight.normal}</Label>
                  <Slider
                    value={[config.typography.fontWeight.normal]}
                    onValueChange={([value]) => updateConfig('typography.fontWeight.normal', value)}
                    min={100}
                    max={900}
                    step={100}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Medium: {config.typography.fontWeight.medium}</Label>
                  <Slider
                    value={[config.typography.fontWeight.medium]}
                    onValueChange={([value]) => updateConfig('typography.fontWeight.medium', value)}
                    min={100}
                    max={900}
                    step={100}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Semibold: {config.typography.fontWeight.semibold}</Label>
                  <Slider
                    value={[config.typography.fontWeight.semibold]}
                    onValueChange={([value]) => updateConfig('typography.fontWeight.semibold', value)}
                    min={100}
                    max={900}
                    step={100}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Bold: {config.typography.fontWeight.bold}</Label>
                  <Slider
                    value={[config.typography.fontWeight.bold]}
                    onValueChange={([value]) => updateConfig('typography.fontWeight.bold', value)}
                    min={100}
                    max={900}
                    step={100}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Spacing Configuration */}
        <TabsContent value="spacing" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Spacing Scale</CardTitle>
                <CardDescription>
                  Configure the spacing system and base units
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Base Unit: {config.spacing.baseUnit}px</Label>
                  <Slider
                    value={[config.spacing.baseUnit]}
                    onValueChange={([value]) => updateConfig('spacing.baseUnit', value)}
                    min={2}
                    max={8}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Scale Factor: {config.spacing.scale}</Label>
                  <Slider
                    value={[config.spacing.scale]}
                    onValueChange={([value]) => updateConfig('spacing.scale', value)}
                    min={0.5}
                    max={2}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Border Radius</CardTitle>
                <CardDescription>
                  Set border radius values for different components
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Small: {config.borderRadius.sm}px</Label>
                  <Slider
                    value={[config.borderRadius.sm]}
                    onValueChange={([value]) => updateConfig('borderRadius.sm', value)}
                    min={0}
                    max={8}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Medium: {config.borderRadius.md}px</Label>
                  <Slider
                    value={[config.borderRadius.md]}
                    onValueChange={([value]) => updateConfig('borderRadius.md', value)}
                    min={0}
                    max={16}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Large: {config.borderRadius.lg}px</Label>
                  <Slider
                    value={[config.borderRadius.lg]}
                    onValueChange={([value]) => updateConfig('borderRadius.lg', value)}
                    min={0}
                    max={24}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Extra Large: {config.borderRadius.xl}px</Label>
                  <Slider
                    value={[config.borderRadius.xl]}
                    onValueChange={([value]) => updateConfig('borderRadius.xl', value)}
                    min={0}
                    max={32}
                    step={1}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Components Configuration */}
        <TabsContent value="components" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Component Styling</CardTitle>
              <CardDescription>
                Preview and configure component-specific styles
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Button Styles</Label>
                  <div className="flex flex-wrap gap-2">
                    <Button size="sm">Small</Button>
                    <Button>Default</Button>
                    <Button size="lg">Large</Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Button variant="outline">Outline</Button>
                    <Button variant="ghost">Ghost</Button>
                    <Button variant="destructive">Destructive</Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Input Styles</Label>
                  <Input placeholder="Default input" />
                  <Input placeholder="Disabled input" disabled />
                </div>

                <div className="space-y-2">
                  <Label>Badge Styles</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge>Default</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge variant="outline">Outline</Badge>
                    <Badge variant="destructive">Destructive</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Animations Configuration */}
        <TabsContent value="animations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Animation Settings</CardTitle>
              <CardDescription>
                Configure animation durations and easing functions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Fast Duration: {config.animations.duration.fast}ms</Label>
                    <Slider
                      value={[config.animations.duration.fast]}
                      onValueChange={([value]) => updateConfig('animations.duration.fast', value)}
                      min={50}
                      max={300}
                      step={10}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Normal Duration: {config.animations.duration.normal}ms</Label>
                    <Slider
                      value={[config.animations.duration.normal]}
                      onValueChange={([value]) => updateConfig('animations.duration.normal', value)}
                      min={100}
                      max={500}
                      step={10}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Slow Duration: {config.animations.duration.slow}ms</Label>
                    <Slider
                      value={[config.animations.duration.slow]}
                      onValueChange={([value]) => updateConfig('animations.duration.slow', value)}
                      min={200}
                      max={1000}
                      step={10}
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Easing Function</Label>
                    <Select
                      value={config.animations.easing}
                      onValueChange={(value) => updateConfig('animations.easing', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cubic-bezier(0.4, 0, 0.2, 1)">Ease Out</SelectItem>
                        <SelectItem value="cubic-bezier(0.4, 0, 1, 1)">Ease In</SelectItem>
                        <SelectItem value="cubic-bezier(0.4, 0, 0.6, 1)">Ease In Out</SelectItem>
                        <SelectItem value="linear">Linear</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <Label className="text-sm font-medium">Animation Preview</Label>
                    <div className="mt-2 h-8 w-8 bg-primary rounded-full animate-bounce"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
