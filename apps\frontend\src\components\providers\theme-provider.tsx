"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import { type ThemeProviderProps } from "next-themes/dist/types"
import { themeConfig, applyThemeVariables, getCurrentThemeVariables } from "@/lib/theme-config"
import { designSystem } from "@/lib/design-system"

// Design System Context
interface DesignSystemContextType {
  designSystem: typeof designSystem;
  theme: {
    mode: string;
    setTheme: (theme: string) => void;
    resolvedTheme: string | undefined;
    themes: string[];
  };
}

const DesignSystemContext = React.createContext<DesignSystemContextType | undefined>(undefined)

// Custom hook to use design system
export function useDesignSystem() {
  const context = React.useContext(DesignSystemContext)
  if (context === undefined) {
    throw new Error("useDesignSystem must be used within a DesignSystemProvider")
  }
  return context
}

// Custom hook to use theme
export function useTheme() {
  const context = React.useContext(DesignSystemContext)
  if (context === undefined) {
    throw new Error("useTheme must be used within a DesignSystemProvider")
  }
  return context.theme
}

// Theme Provider Component
export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute={themeConfig.attribute}
      defaultTheme={themeConfig.defaultTheme}
      enableSystem={themeConfig.enableSystem}
      disableTransitionOnChange={themeConfig.disableTransitionOnChange}
      storageKey={themeConfig.storageKey}
      themes={[...themeConfig.themes]}
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
}

// Design System Provider Component
export function DesignSystemProvider({ 
  children,
  ...themeProps 
}: ThemeProviderProps) {
  const [mounted, setMounted] = React.useState(false)

  // Ensure component is mounted before accessing theme
  React.useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <NextThemesProvider
      attribute={themeConfig.attribute}
      defaultTheme={themeConfig.defaultTheme}
      enableSystem={themeConfig.enableSystem}
      disableTransitionOnChange={themeConfig.disableTransitionOnChange}
      storageKey={themeConfig.storageKey}
      themes={[...themeConfig.themes]}
      {...themeProps}
    >
      <DesignSystemProviderInner mounted={mounted}>
        {children}
      </DesignSystemProviderInner>
    </NextThemesProvider>
  )
}

// Inner provider component that has access to theme context
function DesignSystemProviderInner({ 
  children, 
  mounted 
}: { 
  children: React.ReactNode;
  mounted: boolean;
}) {
  const { theme, setTheme, resolvedTheme, themes } = useThemeFromNextThemes()

  // Apply theme variables when theme changes
  React.useEffect(() => {
    if (!mounted || !resolvedTheme) return

    const themeVariables = getCurrentThemeVariables(
      resolvedTheme as 'light' | 'dark'
    )
    applyThemeVariables(themeVariables)
  }, [resolvedTheme, mounted])

  const contextValue: DesignSystemContextType = {
    designSystem,
    theme: {
      mode: theme,
      setTheme,
      resolvedTheme,
      themes,
    },
  }

  return (
    <DesignSystemContext.Provider value={contextValue}>
      {children}
    </DesignSystemContext.Provider>
  )
}

// Helper hook to get theme from next-themes
function useThemeFromNextThemes() {
  try {
    // Dynamic import to avoid SSR issues
    const { useTheme } = require("next-themes")
    return useTheme()
  } catch {
    // Fallback if next-themes is not available
    return {
      theme: 'light',
      setTheme: () => {},
      resolvedTheme: 'light',
      themes: ['light', 'dark'],
    }
  }
}

// Utility component for theme-aware styling
export function ThemeAwareComponent({ 
  children,
  lightClassName = "",
  darkClassName = "",
  className = "",
}: {
  children: React.ReactNode;
  lightClassName?: string;
  darkClassName?: string;
  className?: string;
}) {
  const { theme } = useTheme()
  
  const themeSpecificClass = theme === 'dark' ? darkClassName : lightClassName
  const combinedClassName = `${className} ${themeSpecificClass}`.trim()

  return (
    <div className={combinedClassName}>
      {children}
    </div>
  )
}

// Hook for responsive design
export function useResponsive() {
  const [windowSize, setWindowSize] = React.useState({
    width: 0,
    height: 0,
  })

  React.useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    // Set initial size
    handleResize()

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const breakpoints = designSystem.breakpoints
  
  return {
    windowSize,
    isMobile: windowSize.width < parseInt(breakpoints.sm),
    isTablet: windowSize.width >= parseInt(breakpoints.sm) && windowSize.width < parseInt(breakpoints.lg),
    isDesktop: windowSize.width >= parseInt(breakpoints.lg),
    isLarge: windowSize.width >= parseInt(breakpoints.xl),
    isXLarge: windowSize.width >= parseInt(breakpoints['2xl']),
  }
}

// Hook for accessing design tokens
export function useDesignTokens() {
  const { designSystem } = useDesignSystem()
  
  return {
    colors: designSystem.colors,
    typography: designSystem.typography,
    spacing: designSystem.spacing,
    borderRadius: designSystem.borderRadius,
    shadows: designSystem.shadows,
    animations: designSystem.animations,
    breakpoints: designSystem.breakpoints,
    zIndex: designSystem.zIndex,
    componentVariants: designSystem.componentVariants,
  }
}

// Export types
export type { DesignSystemContextType, ThemeProviderProps }
