/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M2 9.35V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h7",
      key: "y8kt7d"
    }
  ],
  ["path", { d: "m8 16 3-3-3-3", key: "rlqrt1" }]
];
const FolderSymlink = createLucideIcon("folder-symlink", __iconNode);

export { __iconNode, FolderSymlink as default };
//# sourceMappingURL=folder-symlink.js.map
