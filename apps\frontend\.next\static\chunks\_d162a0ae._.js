(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/design-system.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Centralized Design System Configuration
 * All design tokens, theme configuration, and styling constants
 */ // Typography Configuration
__turbopack_context__.s({
    "animations": ()=>animations,
    "borderRadius": ()=>borderRadius,
    "breakpoints": ()=>breakpoints,
    "colors": ()=>colors,
    "componentVariants": ()=>componentVariants,
    "designSystem": ()=>designSystem,
    "shadows": ()=>shadows,
    "spacing": ()=>spacing,
    "typography": ()=>typography,
    "zIndex": ()=>zIndex
});
const typography = {
    fontFamilies: {
        sans: [
            'var(--font-geist-sans)',
            'system-ui',
            'sans-serif'
        ],
        mono: [
            'var(--font-geist-mono)',
            'Consolas',
            'monospace'
        ]
    },
    fontSizes: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
        '6xl': '3.75rem',
        '7xl': '4.5rem',
        '8xl': '6rem',
        '9xl': '8rem'
    },
    fontWeights: {
        thin: '100',
        extralight: '200',
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        extrabold: '800',
        black: '900'
    },
    lineHeights: {
        none: '1',
        tight: '1.25',
        snug: '1.375',
        normal: '1.5',
        relaxed: '1.625',
        loose: '2'
    },
    letterSpacing: {
        tighter: '-0.05em',
        tight: '-0.025em',
        normal: '0em',
        wide: '0.025em',
        wider: '0.05em',
        widest: '0.1em'
    }
};
const colors = {
    // Primary brand colors
    primary: {
        50: 'oklch(0.985 0.001 106.423)',
        100: 'oklch(0.97 0.001 106.424)',
        200: 'oklch(0.923 0.003 48.717)',
        300: 'oklch(0.709 0.01 56.259)',
        400: 'oklch(0.553 0.013 58.071)',
        500: 'oklch(0.216 0.006 56.043)',
        600: 'oklch(0.147 0.004 49.25)',
        700: 'oklch(0.147 0.004 49.25)',
        800: 'oklch(0.147 0.004 49.25)',
        900: 'oklch(0.147 0.004 49.25)',
        950: 'oklch(0.147 0.004 49.25)'
    },
    // Secondary colors
    secondary: {
        50: 'oklch(0.985 0.001 106.423)',
        100: 'oklch(0.97 0.001 106.424)',
        200: 'oklch(0.923 0.003 48.717)',
        300: 'oklch(0.709 0.01 56.259)',
        400: 'oklch(0.553 0.013 58.071)',
        500: 'oklch(0.268 0.007 34.298)',
        600: 'oklch(0.216 0.006 56.043)',
        700: 'oklch(0.147 0.004 49.25)',
        800: 'oklch(0.147 0.004 49.25)',
        900: 'oklch(0.147 0.004 49.25)',
        950: 'oklch(0.147 0.004 49.25)'
    },
    // Accent colors
    accent: {
        50: 'oklch(0.985 0.001 106.423)',
        100: 'oklch(0.97 0.001 106.424)',
        200: 'oklch(0.923 0.003 48.717)',
        300: 'oklch(0.709 0.01 56.259)',
        400: 'oklch(0.553 0.013 58.071)',
        500: 'oklch(0.268 0.007 34.298)',
        600: 'oklch(0.216 0.006 56.043)',
        700: 'oklch(0.147 0.004 49.25)',
        800: 'oklch(0.147 0.004 49.25)',
        900: 'oklch(0.147 0.004 49.25)',
        950: 'oklch(0.147 0.004 49.25)'
    },
    // Neutral colors
    neutral: {
        50: 'oklch(0.985 0.001 106.423)',
        100: 'oklch(0.97 0.001 106.424)',
        200: 'oklch(0.923 0.003 48.717)',
        300: 'oklch(0.709 0.01 56.259)',
        400: 'oklch(0.553 0.013 58.071)',
        500: 'oklch(0.268 0.007 34.298)',
        600: 'oklch(0.216 0.006 56.043)',
        700: 'oklch(0.147 0.004 49.25)',
        800: 'oklch(0.147 0.004 49.25)',
        900: 'oklch(0.147 0.004 49.25)',
        950: 'oklch(0.147 0.004 49.25)'
    },
    // Semantic colors
    success: {
        50: 'oklch(0.95 0.05 142)',
        100: 'oklch(0.9 0.1 142)',
        200: 'oklch(0.85 0.15 142)',
        300: 'oklch(0.8 0.2 142)',
        400: 'oklch(0.75 0.25 142)',
        500: 'oklch(0.7 0.3 142)',
        600: 'oklch(0.65 0.35 142)',
        700: 'oklch(0.6 0.4 142)',
        800: 'oklch(0.55 0.45 142)',
        900: 'oklch(0.5 0.5 142)',
        950: 'oklch(0.45 0.55 142)'
    },
    warning: {
        50: 'oklch(0.95 0.05 85)',
        100: 'oklch(0.9 0.1 85)',
        200: 'oklch(0.85 0.15 85)',
        300: 'oklch(0.8 0.2 85)',
        400: 'oklch(0.75 0.25 85)',
        500: 'oklch(0.7 0.3 85)',
        600: 'oklch(0.65 0.35 85)',
        700: 'oklch(0.6 0.4 85)',
        800: 'oklch(0.55 0.45 85)',
        900: 'oklch(0.5 0.5 85)',
        950: 'oklch(0.45 0.55 85)'
    },
    error: {
        50: 'oklch(0.95 0.05 27)',
        100: 'oklch(0.9 0.1 27)',
        200: 'oklch(0.85 0.15 27)',
        300: 'oklch(0.8 0.2 27)',
        400: 'oklch(0.75 0.25 27)',
        500: 'oklch(0.577 0.245 27.325)',
        600: 'oklch(0.65 0.35 27)',
        700: 'oklch(0.6 0.4 27)',
        800: 'oklch(0.55 0.45 27)',
        900: 'oklch(0.5 0.5 27)',
        950: 'oklch(0.45 0.55 27)'
    }
};
const spacing = {
    px: '1px',
    0: '0px',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem'
};
const borderRadius = {
    none: '0px',
    sm: 'calc(var(--radius) - 4px)',
    md: 'calc(var(--radius) - 2px)',
    lg: 'var(--radius)',
    xl: 'calc(var(--radius) + 4px)',
    '2xl': 'calc(var(--radius) + 8px)',
    '3xl': 'calc(var(--radius) + 12px)',
    full: '9999px'
};
const shadows = {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: '0 0 #0000'
};
const animations = {
    durations: {
        75: '75ms',
        100: '100ms',
        150: '150ms',
        200: '200ms',
        300: '300ms',
        500: '500ms',
        700: '700ms',
        1000: '1000ms'
    },
    timingFunctions: {
        linear: 'linear',
        in: 'cubic-bezier(0.4, 0, 1, 1)',
        out: 'cubic-bezier(0, 0, 0.2, 1)',
        'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
};
const breakpoints = {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
};
const zIndex = {
    auto: 'auto',
    0: '0',
    10: '10',
    20: '20',
    30: '30',
    40: '40',
    50: '50',
    dropdown: '1000',
    sticky: '1020',
    fixed: '1030',
    modal: '1040',
    popover: '1050',
    tooltip: '1060',
    toast: '1070'
};
const componentVariants = {
    button: {
        sizes: {
            sm: 'h-8 px-3 text-xs',
            md: 'h-9 px-4 py-2',
            lg: 'h-10 px-8',
            xl: 'h-11 px-8',
            icon: 'h-9 w-9'
        },
        variants: {
            default: 'bg-primary text-primary-foreground shadow hover:bg-primary/90',
            destructive: 'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
            outline: 'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
            secondary: 'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
            ghost: 'hover:bg-accent hover:text-accent-foreground',
            link: 'text-primary underline-offset-4 hover:underline'
        }
    },
    input: {
        sizes: {
            sm: 'h-8 px-3 text-xs',
            md: 'h-9 px-3',
            lg: 'h-10 px-3'
        }
    },
    card: {
        variants: {
            default: 'border bg-card text-card-foreground shadow',
            elevated: 'border bg-card text-card-foreground shadow-lg',
            outline: 'border-2 bg-card text-card-foreground'
        }
    }
};
const designSystem = {
    typography,
    colors,
    spacing,
    borderRadius,
    shadows,
    animations,
    breakpoints,
    zIndex,
    componentVariants
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/theme-config.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Theme Configuration
 * Defines light and dark theme configurations using the design system
 */ __turbopack_context__.s({
    "applyThemeVariables": ()=>applyThemeVariables,
    "darkTheme": ()=>darkTheme,
    "getCurrentThemeVariables": ()=>getCurrentThemeVariables,
    "lightTheme": ()=>lightTheme,
    "styledTheme": ()=>styledTheme,
    "themeConfig": ()=>themeConfig
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/design-system.ts [app-client] (ecmascript)");
;
const lightTheme = {
    // Base colors
    '--background': 'oklch(1 0 0)',
    '--foreground': 'oklch(0.147 0.004 49.25)',
    // Card colors
    '--card': 'oklch(1 0 0)',
    '--card-foreground': 'oklch(0.147 0.004 49.25)',
    // Popover colors
    '--popover': 'oklch(1 0 0)',
    '--popover-foreground': 'oklch(0.147 0.004 49.25)',
    // Primary colors
    '--primary': 'oklch(0.216 0.006 56.043)',
    '--primary-foreground': 'oklch(0.985 0.001 106.423)',
    // Secondary colors
    '--secondary': 'oklch(0.97 0.001 106.424)',
    '--secondary-foreground': 'oklch(0.216 0.006 56.043)',
    // Muted colors
    '--muted': 'oklch(0.97 0.001 106.424)',
    '--muted-foreground': 'oklch(0.553 0.013 58.071)',
    // Accent colors
    '--accent': 'oklch(0.97 0.001 106.424)',
    '--accent-foreground': 'oklch(0.216 0.006 56.043)',
    // Destructive colors
    '--destructive': 'oklch(0.577 0.245 27.325)',
    '--destructive-foreground': 'oklch(0.985 0.001 106.423)',
    // Border and input colors
    '--border': 'oklch(0.923 0.003 48.717)',
    '--input': 'oklch(0.923 0.003 48.717)',
    '--ring': 'oklch(0.709 0.01 56.259)',
    // Chart colors
    '--chart-1': 'oklch(0.646 0.222 41.116)',
    '--chart-2': 'oklch(0.6 0.118 184.704)',
    '--chart-3': 'oklch(0.398 0.07 227.392)',
    '--chart-4': 'oklch(0.828 0.189 84.429)',
    '--chart-5': 'oklch(0.769 0.188 70.08)',
    // Sidebar colors
    '--sidebar': 'oklch(0.985 0.001 106.423)',
    '--sidebar-foreground': 'oklch(0.147 0.004 49.25)',
    '--sidebar-primary': 'oklch(0.216 0.006 56.043)',
    '--sidebar-primary-foreground': 'oklch(0.985 0.001 106.423)',
    '--sidebar-accent': 'oklch(0.97 0.001 106.424)',
    '--sidebar-accent-foreground': 'oklch(0.216 0.006 56.043)',
    '--sidebar-border': 'oklch(0.923 0.003 48.717)',
    '--sidebar-ring': 'oklch(0.709 0.01 56.259)',
    // Border radius
    '--radius': '0.625rem'
};
const darkTheme = {
    // Base colors
    '--background': 'oklch(0.147 0.004 49.25)',
    '--foreground': 'oklch(0.985 0.001 106.423)',
    // Card colors
    '--card': 'oklch(0.216 0.006 56.043)',
    '--card-foreground': 'oklch(0.985 0.001 106.423)',
    // Popover colors
    '--popover': 'oklch(0.216 0.006 56.043)',
    '--popover-foreground': 'oklch(0.985 0.001 106.423)',
    // Primary colors
    '--primary': 'oklch(0.923 0.003 48.717)',
    '--primary-foreground': 'oklch(0.216 0.006 56.043)',
    // Secondary colors
    '--secondary': 'oklch(0.268 0.007 34.298)',
    '--secondary-foreground': 'oklch(0.985 0.001 106.423)',
    // Muted colors
    '--muted': 'oklch(0.268 0.007 34.298)',
    '--muted-foreground': 'oklch(0.709 0.01 56.259)',
    // Accent colors
    '--accent': 'oklch(0.268 0.007 34.298)',
    '--accent-foreground': 'oklch(0.985 0.001 106.423)',
    // Destructive colors
    '--destructive': 'oklch(0.704 0.191 22.216)',
    '--destructive-foreground': 'oklch(0.985 0.001 106.423)',
    // Border and input colors
    '--border': 'oklch(1 0 0 / 10%)',
    '--input': 'oklch(1 0 0 / 15%)',
    '--ring': 'oklch(0.553 0.013 58.071)',
    // Chart colors
    '--chart-1': 'oklch(0.488 0.243 264.376)',
    '--chart-2': 'oklch(0.696 0.17 162.48)',
    '--chart-3': 'oklch(0.769 0.188 70.08)',
    '--chart-4': 'oklch(0.627 0.265 303.9)',
    '--chart-5': 'oklch(0.645 0.246 16.439)',
    // Sidebar colors
    '--sidebar': 'oklch(0.216 0.006 56.043)',
    '--sidebar-foreground': 'oklch(0.985 0.001 106.423)',
    '--sidebar-primary': 'oklch(0.488 0.243 264.376)',
    '--sidebar-primary-foreground': 'oklch(0.985 0.001 106.423)',
    '--sidebar-accent': 'oklch(0.268 0.007 34.298)',
    '--sidebar-accent-foreground': 'oklch(0.985 0.001 106.423)',
    '--sidebar-border': 'oklch(1 0 0 / 10%)',
    '--sidebar-ring': 'oklch(0.553 0.013 58.071)',
    // Border radius
    '--radius': '0.625rem'
};
const themeConfig = {
    light: lightTheme,
    dark: darkTheme,
    defaultTheme: 'system',
    enableSystem: true,
    disableTransitionOnChange: false,
    storageKey: 'loni-theme',
    attribute: 'class',
    defaultClass: 'light',
    themes: [
        'light',
        'dark'
    ]
};
const applyThemeVariables = (theme)=>{
    if (typeof document === 'undefined') return;
    const root = document.documentElement;
    Object.entries(theme).forEach((param)=>{
        let [property, value] = param;
        root.style.setProperty(property, value);
    });
};
const getCurrentThemeVariables = (mode)=>{
    return mode === 'light' ? lightTheme : darkTheme;
};
const styledTheme = {
    light: {
        colors: {
            background: 'var(--background)',
            foreground: 'var(--foreground)',
            card: 'var(--card)',
            cardForeground: 'var(--card-foreground)',
            popover: 'var(--popover)',
            popoverForeground: 'var(--popover-foreground)',
            primary: 'var(--primary)',
            primaryForeground: 'var(--primary-foreground)',
            secondary: 'var(--secondary)',
            secondaryForeground: 'var(--secondary-foreground)',
            muted: 'var(--muted)',
            mutedForeground: 'var(--muted-foreground)',
            accent: 'var(--accent)',
            accentForeground: 'var(--accent-foreground)',
            destructive: 'var(--destructive)',
            destructiveForeground: 'var(--destructive-foreground)',
            border: 'var(--border)',
            input: 'var(--input)',
            ring: 'var(--ring)'
        },
        spacing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].spacing,
        borderRadius: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].borderRadius,
        shadows: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].shadows,
        typography: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].typography,
        animations: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].animations,
        breakpoints: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].breakpoints,
        zIndex: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].zIndex
    },
    dark: {
        colors: {
            background: 'var(--background)',
            foreground: 'var(--foreground)',
            card: 'var(--card)',
            cardForeground: 'var(--card-foreground)',
            popover: 'var(--popover)',
            popoverForeground: 'var(--popover-foreground)',
            primary: 'var(--primary)',
            primaryForeground: 'var(--primary-foreground)',
            secondary: 'var(--secondary)',
            secondaryForeground: 'var(--secondary-foreground)',
            muted: 'var(--muted)',
            mutedForeground: 'var(--muted-foreground)',
            accent: 'var(--accent)',
            accentForeground: 'var(--accent-foreground)',
            destructive: 'var(--destructive)',
            destructiveForeground: 'var(--destructive-foreground)',
            border: 'var(--border)',
            input: 'var(--input)',
            ring: 'var(--ring)'
        },
        spacing: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].spacing,
        borderRadius: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].borderRadius,
        shadows: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].shadows,
        typography: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].typography,
        animations: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].animations,
        breakpoints: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].breakpoints,
        zIndex: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].zIndex
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/providers/theme-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DesignSystemProvider": ()=>DesignSystemProvider,
    "ThemeAwareComponent": ()=>ThemeAwareComponent,
    "ThemeProvider": ()=>ThemeProvider,
    "useDesignSystem": ()=>useDesignSystem,
    "useDesignTokens": ()=>useDesignTokens,
    "useResponsive": ()=>useResponsive,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/theme-config.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/design-system.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
const DesignSystemContext = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"](undefined);
function useDesignSystem() {
    _s();
    const context = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"](DesignSystemContext);
    if (context === undefined) {
        throw new Error("useDesignSystem must be used within a DesignSystemProvider");
    }
    return context;
}
_s(useDesignSystem, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useTheme() {
    _s1();
    const context = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"](DesignSystemContext);
    if (context === undefined) {
        throw new Error("useTheme must be used within a DesignSystemProvider");
    }
    return context.theme;
}
_s1(useTheme, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function ThemeProvider(param) {
    let { children, ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        attribute: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].attribute,
        defaultTheme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].defaultTheme,
        enableSystem: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].enableSystem,
        disableTransitionOnChange: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].disableTransitionOnChange,
        storageKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].storageKey,
        themes: [
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].themes
        ],
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/theme-provider.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
_c = ThemeProvider;
function DesignSystemProvider(param) {
    let { children, ...themeProps } = param;
    _s2();
    const [mounted, setMounted] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](false);
    // Ensure component is mounted before accessing theme
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "DesignSystemProvider.useEffect": ()=>{
            setMounted(true);
        }
    }["DesignSystemProvider.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        attribute: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].attribute,
        defaultTheme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].defaultTheme,
        enableSystem: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].enableSystem,
        disableTransitionOnChange: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].disableTransitionOnChange,
        storageKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].storageKey,
        themes: [
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["themeConfig"].themes
        ],
        ...themeProps,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DesignSystemProviderInner, {
            mounted: mounted,
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/providers/theme-provider.tsx",
            lineNumber: 79,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/providers/theme-provider.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
_s2(DesignSystemProvider, "LrrVfNW3d1raFE0BNzCTILYmIfo=");
_c1 = DesignSystemProvider;
// Inner provider component that has access to theme context
function DesignSystemProviderInner(param) {
    let { children, mounted } = param;
    _s3();
    const { theme, setTheme, resolvedTheme, themes } = useThemeFromNextThemes();
    // Apply theme variables when theme changes
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "DesignSystemProviderInner.useEffect": ()=>{
            if (!mounted || !resolvedTheme) return;
            const themeVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentThemeVariables"])(resolvedTheme);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$theme$2d$config$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applyThemeVariables"])(themeVariables);
        }
    }["DesignSystemProviderInner.useEffect"], [
        resolvedTheme,
        mounted
    ]);
    const contextValue = {
        designSystem: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"],
        theme: {
            mode: theme,
            setTheme,
            resolvedTheme,
            themes
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DesignSystemContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/theme-provider.tsx",
        lineNumber: 117,
        columnNumber: 5
    }, this);
}
_s3(DesignSystemProviderInner, "nf2tfBtDQOPUf6Y9hPhlTkSyA/g=", false, function() {
    return [
        useThemeFromNextThemes
    ];
});
_c2 = DesignSystemProviderInner;
// Helper hook to get theme from next-themes
function useThemeFromNextThemes() {
    try {
        // Dynamic import to avoid SSR issues
        const { useTheme } = __turbopack_context__.r("[project]/node_modules/next-themes/dist/index.js [app-client] (ecmascript)");
        return useTheme();
    } catch (e) {
        // Fallback if next-themes is not available
        return {
            theme: 'light',
            setTheme: ()=>{},
            resolvedTheme: 'light',
            themes: [
                'light',
                'dark'
            ]
        };
    }
}
function ThemeAwareComponent(param) {
    let { children, lightClassName = "", darkClassName = "", className = "" } = param;
    _s4();
    const { theme } = useTheme();
    const themeSpecificClass = theme === 'dark' ? darkClassName : lightClassName;
    const combinedClassName = "".concat(className, " ").concat(themeSpecificClass).trim();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: combinedClassName,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/providers/theme-provider.tsx",
        lineNumber: 158,
        columnNumber: 5
    }, this);
}
_s4(ThemeAwareComponent, "JkSxfi8+JQlqgIgDOc3wQN+nVIw=", false, function() {
    return [
        useTheme
    ];
});
_c3 = ThemeAwareComponent;
function useResponsive() {
    _s5();
    const [windowSize, setWindowSize] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"]({
        width: 0,
        height: 0
    });
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "useResponsive.useEffect": ()=>{
            function handleResize() {
                setWindowSize({
                    width: window.innerWidth,
                    height: window.innerHeight
                });
            }
            // Set initial size
            handleResize();
            window.addEventListener('resize', handleResize);
            return ({
                "useResponsive.useEffect": ()=>window.removeEventListener('resize', handleResize)
            })["useResponsive.useEffect"];
        }
    }["useResponsive.useEffect"], []);
    const breakpoints = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$design$2d$system$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designSystem"].breakpoints;
    return {
        windowSize,
        isMobile: windowSize.width < parseInt(breakpoints.sm),
        isTablet: windowSize.width >= parseInt(breakpoints.sm) && windowSize.width < parseInt(breakpoints.lg),
        isDesktop: windowSize.width >= parseInt(breakpoints.lg),
        isLarge: windowSize.width >= parseInt(breakpoints.xl),
        isXLarge: windowSize.width >= parseInt(breakpoints['2xl'])
    };
}
_s5(useResponsive, "u4T2YQmxFMlb92DDoJhpQy4d8Zc=");
function useDesignTokens() {
    _s6();
    const { designSystem } = useDesignSystem();
    return {
        colors: designSystem.colors,
        typography: designSystem.typography,
        spacing: designSystem.spacing,
        borderRadius: designSystem.borderRadius,
        shadows: designSystem.shadows,
        animations: designSystem.animations,
        breakpoints: designSystem.breakpoints,
        zIndex: designSystem.zIndex,
        componentVariants: designSystem.componentVariants
    };
}
_s6(useDesignTokens, "i396vkLqts6lgd8DkBetpdAbTiE=", false, function() {
    return [
        useDesignSystem
    ];
});
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "ThemeProvider");
__turbopack_context__.k.register(_c1, "DesignSystemProvider");
__turbopack_context__.k.register(_c2, "DesignSystemProviderInner");
__turbopack_context__.k.register(_c3, "ThemeAwareComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return type.displayName || "Context";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler"), REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        react_stack_bottom_frame: function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ThemeProvider": ()=>J,
    "useTheme": ()=>z
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
"use client";
;
var M = (e, i, s, u, m, a, l, h)=>{
    let d = document.documentElement, w = [
        "light",
        "dark"
    ];
    function p(n) {
        (Array.isArray(e) ? e : [
            e
        ]).forEach((y)=>{
            let k = y === "class", S = k && a ? m.map((f)=>a[f] || f) : m;
            k ? (d.classList.remove(...S), d.classList.add(a && a[n] ? a[n] : n)) : d.setAttribute(y, n);
        }), R(n);
    }
    function R(n) {
        h && w.includes(n) && (d.style.colorScheme = n);
    }
    function c() {
        return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
    if (u) p(u);
    else try {
        let n = localStorage.getItem(i) || s, y = l && n === "system" ? c() : n;
        p(y);
    } catch (n) {}
};
var b = [
    "light",
    "dark"
], I = "(prefers-color-scheme: dark)", O = typeof window == "undefined", x = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"](void 0), U = {
    setTheme: (e)=>{},
    themes: []
}, z = ()=>{
    var e;
    return (e = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"](x)) != null ? e : U;
}, J = (e)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"](x) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, e.children) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](V, {
        ...e
    }), N = [
    "light",
    "dark"
], V = (param)=>{
    let { forcedTheme: e, disableTransitionOnChange: i = !1, enableSystem: s = !0, enableColorScheme: u = !0, storageKey: m = "theme", themes: a = N, defaultTheme: l = s ? "system" : "light", attribute: h = "data-theme", value: d, children: w, nonce: p, scriptProps: R } = param;
    let [c, n] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"]({
        "V.useState": ()=>H(m, l)
    }["V.useState"]), [T, y] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"]({
        "V.useState": ()=>c === "system" ? E() : c
    }["V.useState"]), k = d ? Object.values(d) : a, S = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "V.useCallback[S]": (o)=>{
            let r = o;
            if (!r) return;
            o === "system" && s && (r = E());
            let v = d ? d[r] : r, C = i ? W(p) : null, P = document.documentElement, L = {
                "V.useCallback[S].L": (g)=>{
                    g === "class" ? (P.classList.remove(...k), v && P.classList.add(v)) : g.startsWith("data-") && (v ? P.setAttribute(g, v) : P.removeAttribute(g));
                }
            }["V.useCallback[S].L"];
            if (Array.isArray(h) ? h.forEach(L) : L(h), u) {
                let g = b.includes(l) ? l : null, D = b.includes(r) ? r : g;
                P.style.colorScheme = D;
            }
            C == null || C();
        }
    }["V.useCallback[S]"], [
        p
    ]), f = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "V.useCallback[f]": (o)=>{
            let r = typeof o == "function" ? o(c) : o;
            n(r);
            try {
                localStorage.setItem(m, r);
            } catch (v) {}
        }
    }["V.useCallback[f]"], [
        c
    ]), A = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "V.useCallback[A]": (o)=>{
            let r = E(o);
            y(r), c === "system" && s && !e && S("system");
        }
    }["V.useCallback[A]"], [
        c,
        e
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "V.useEffect": ()=>{
            let o = window.matchMedia(I);
            return o.addListener(A), A(o), ({
                "V.useEffect": ()=>o.removeListener(A)
            })["V.useEffect"];
        }
    }["V.useEffect"], [
        A
    ]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "V.useEffect": ()=>{
            let o = {
                "V.useEffect.o": (r)=>{
                    r.key === m && (r.newValue ? n(r.newValue) : f(l));
                }
            }["V.useEffect.o"];
            return window.addEventListener("storage", o), ({
                "V.useEffect": ()=>window.removeEventListener("storage", o)
            })["V.useEffect"];
        }
    }["V.useEffect"], [
        f
    ]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "V.useEffect": ()=>{
            S(e != null ? e : c);
        }
    }["V.useEffect"], [
        e,
        c
    ]);
    let Q = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "V.useMemo[Q]": ()=>({
                theme: c,
                setTheme: f,
                forcedTheme: e,
                resolvedTheme: c === "system" ? T : c,
                themes: s ? [
                    ...a,
                    "system"
                ] : a,
                systemTheme: s ? T : void 0
            })
    }["V.useMemo[Q]"], [
        c,
        f,
        e,
        T,
        s,
        a
    ]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](x.Provider, {
        value: Q
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](_, {
        forcedTheme: e,
        storageKey: m,
        attribute: h,
        enableSystem: s,
        enableColorScheme: u,
        defaultTheme: l,
        value: d,
        themes: a,
        nonce: p,
        scriptProps: R
    }), w);
}, _ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"]((param)=>{
    let { forcedTheme: e, storageKey: i, attribute: s, enableSystem: u, enableColorScheme: m, defaultTheme: a, value: l, themes: h, nonce: d, scriptProps: w } = param;
    let p = JSON.stringify([
        s,
        i,
        a,
        e,
        h,
        l,
        u,
        m
    ]).slice(1, -1);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("script", {
        ...w,
        suppressHydrationWarning: !0,
        nonce: typeof window == "undefined" ? d : "",
        dangerouslySetInnerHTML: {
            __html: "(".concat(M.toString(), ")(").concat(p, ")")
        }
    });
}), H = (e, i)=>{
    if (O) return;
    let s;
    try {
        s = localStorage.getItem(e) || void 0;
    } catch (u) {}
    return s || i;
}, W = (e)=>{
    let i = document.createElement("style");
    return e && i.setAttribute("nonce", e), i.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")), document.head.appendChild(i), ()=>{
        window.getComputedStyle(document.body), setTimeout(()=>{
            document.head.removeChild(i);
        }, 1);
    };
}, E = (e)=>(e || (e = window.matchMedia(I)), e.matches ? "dark" : "light");
;
}),
"[project]/node_modules/next-themes/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var N = Object.create;
var R = Object.defineProperty;
var V = Object.getOwnPropertyDescriptor;
var _ = Object.getOwnPropertyNames;
var H = Object.getPrototypeOf, W = Object.prototype.hasOwnProperty;
var $ = (e, s)=>{
    for(var n in s)R(e, n, {
        get: s[n],
        enumerable: !0
    });
}, b = (e, s, n, l)=>{
    if (s && typeof s == "object" || typeof s == "function") for (let o of _(s))!W.call(e, o) && o !== n && R(e, o, {
        get: ()=>s[o],
        enumerable: !(l = V(s, o)) || l.enumerable
    });
    return e;
};
var j = (e, s, n)=>(n = e != null ? N(H(e)) : {}, b(s || !e || !e.__esModule ? R(n, "default", {
        value: e,
        enumerable: !0
    }) : n, e)), z = (e)=>b(R({}, "__esModule", {
        value: !0
    }), e);
var ee = {};
$(ee, {
    ThemeProvider: ()=>F,
    useTheme: ()=>B
});
module.exports = z(ee);
var t = j(__turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"));
var I = (e, s, n, l, o, d, u, h)=>{
    let m = document.documentElement, w = [
        "light",
        "dark"
    ];
    function p(r) {
        (Array.isArray(e) ? e : [
            e
        ]).forEach((y)=>{
            let k = y === "class", S = k && d ? o.map((f)=>d[f] || f) : o;
            k ? (m.classList.remove(...S), m.classList.add(d && d[r] ? d[r] : r)) : m.setAttribute(y, r);
        }), C(r);
    }
    function C(r) {
        h && w.includes(r) && (m.style.colorScheme = r);
    }
    function a() {
        return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
    if (l) p(l);
    else try {
        let r = localStorage.getItem(s) || n, y = u && r === "system" ? a() : r;
        p(y);
    } catch (r) {}
};
var Q = [
    "light",
    "dark"
], D = "(prefers-color-scheme: dark)", J = typeof window == "undefined", L = t.createContext(void 0), q = {
    setTheme: (e)=>{},
    themes: []
}, B = ()=>{
    var e;
    return (e = t.useContext(L)) != null ? e : q;
}, F = (e)=>t.useContext(L) ? t.createElement(t.Fragment, null, e.children) : t.createElement(X, {
        ...e
    }), G = [
    "light",
    "dark"
], X = (param)=>{
    let { forcedTheme: e, disableTransitionOnChange: s = !1, enableSystem: n = !0, enableColorScheme: l = !0, storageKey: o = "theme", themes: d = G, defaultTheme: u = n ? "system" : "light", attribute: h = "data-theme", value: m, children: w, nonce: p, scriptProps: C } = param;
    let [a, r] = t.useState({
        "X.useState": ()=>Z(o, u)
    }["X.useState"]), [T, y] = t.useState({
        "X.useState": ()=>a === "system" ? x() : a
    }["X.useState"]), k = m ? Object.values(m) : d, S = t.useCallback({
        "X.useCallback[S]": (i)=>{
            let c = i;
            if (!c) return;
            i === "system" && n && (c = x());
            let v = m ? m[c] : c, E = s ? K(p) : null, P = document.documentElement, M = {
                "X.useCallback[S].M": (g)=>{
                    g === "class" ? (P.classList.remove(...k), v && P.classList.add(v)) : g.startsWith("data-") && (v ? P.setAttribute(g, v) : P.removeAttribute(g));
                }
            }["X.useCallback[S].M"];
            if (Array.isArray(h) ? h.forEach(M) : M(h), l) {
                let g = Q.includes(u) ? u : null, U = Q.includes(c) ? c : g;
                P.style.colorScheme = U;
            }
            E == null || E();
        }
    }["X.useCallback[S]"], [
        p
    ]), f = t.useCallback({
        "X.useCallback[f]": (i)=>{
            let c = typeof i == "function" ? i(a) : i;
            r(c);
            try {
                localStorage.setItem(o, c);
            } catch (v) {}
        }
    }["X.useCallback[f]"], [
        a
    ]), A = t.useCallback({
        "X.useCallback[A]": (i)=>{
            let c = x(i);
            y(c), a === "system" && n && !e && S("system");
        }
    }["X.useCallback[A]"], [
        a,
        e
    ]);
    t.useEffect({
        "X.useEffect": ()=>{
            let i = window.matchMedia(D);
            return i.addListener(A), A(i), ({
                "X.useEffect": ()=>i.removeListener(A)
            })["X.useEffect"];
        }
    }["X.useEffect"], [
        A
    ]), t.useEffect({
        "X.useEffect": ()=>{
            let i = {
                "X.useEffect.i": (c)=>{
                    c.key === o && (c.newValue ? r(c.newValue) : f(u));
                }
            }["X.useEffect.i"];
            return window.addEventListener("storage", i), ({
                "X.useEffect": ()=>window.removeEventListener("storage", i)
            })["X.useEffect"];
        }
    }["X.useEffect"], [
        f
    ]), t.useEffect({
        "X.useEffect": ()=>{
            S(e != null ? e : a);
        }
    }["X.useEffect"], [
        e,
        a
    ]);
    let O = t.useMemo({
        "X.useMemo[O]": ()=>({
                theme: a,
                setTheme: f,
                forcedTheme: e,
                resolvedTheme: a === "system" ? T : a,
                themes: n ? [
                    ...d,
                    "system"
                ] : d,
                systemTheme: n ? T : void 0
            })
    }["X.useMemo[O]"], [
        a,
        f,
        e,
        T,
        n,
        d
    ]);
    return t.createElement(L.Provider, {
        value: O
    }, t.createElement(Y, {
        forcedTheme: e,
        storageKey: o,
        attribute: h,
        enableSystem: n,
        enableColorScheme: l,
        defaultTheme: u,
        value: m,
        themes: d,
        nonce: p,
        scriptProps: C
    }), w);
}, Y = t.memo((param)=>{
    let { forcedTheme: e, storageKey: s, attribute: n, enableSystem: l, enableColorScheme: o, defaultTheme: d, value: u, themes: h, nonce: m, scriptProps: w } = param;
    let p = JSON.stringify([
        n,
        s,
        d,
        e,
        h,
        u,
        l,
        o
    ]).slice(1, -1);
    return t.createElement("script", {
        ...w,
        suppressHydrationWarning: !0,
        nonce: typeof window == "undefined" ? m : "",
        dangerouslySetInnerHTML: {
            __html: "(".concat(I.toString(), ")(").concat(p, ")")
        }
    });
}), Z = (e, s)=>{
    if (J) return;
    let n;
    try {
        n = localStorage.getItem(e) || void 0;
    } catch (l) {}
    return n || s;
}, K = (e)=>{
    let s = document.createElement("style");
    return e && s.setAttribute("nonce", e), s.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")), document.head.appendChild(s), ()=>{
        window.getComputedStyle(document.body), setTimeout(()=>{
            document.head.removeChild(s);
        }, 1);
    };
}, x = (e)=>(e || (e = window.matchMedia(D)), e.matches ? "dark" : "light");
0 && (module.exports = {
    ThemeProvider,
    useTheme
});
}}),
}]);

//# sourceMappingURL=_d162a0ae._.js.map