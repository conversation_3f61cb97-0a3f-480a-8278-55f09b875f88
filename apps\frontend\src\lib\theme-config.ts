/**
 * Theme Configuration
 * Defines light and dark theme configurations using the design system
 */

import { designSystem } from './design-system';

// Theme mode type
export type ThemeMode = 'light' | 'dark' | 'system';

// CSS Custom Properties for themes
export const lightTheme = {
  // Base colors
  '--background': 'oklch(1 0 0)',
  '--foreground': 'oklch(0.147 0.004 49.25)',
  
  // Card colors
  '--card': 'oklch(1 0 0)',
  '--card-foreground': 'oklch(0.147 0.004 49.25)',
  
  // Popover colors
  '--popover': 'oklch(1 0 0)',
  '--popover-foreground': 'oklch(0.147 0.004 49.25)',
  
  // Primary colors
  '--primary': 'oklch(0.216 0.006 56.043)',
  '--primary-foreground': 'oklch(0.985 0.001 106.423)',
  
  // Secondary colors
  '--secondary': 'oklch(0.97 0.001 106.424)',
  '--secondary-foreground': 'oklch(0.216 0.006 56.043)',
  
  // Muted colors
  '--muted': 'oklch(0.97 0.001 106.424)',
  '--muted-foreground': 'oklch(0.553 0.013 58.071)',
  
  // Accent colors
  '--accent': 'oklch(0.97 0.001 106.424)',
  '--accent-foreground': 'oklch(0.216 0.006 56.043)',
  
  // Destructive colors
  '--destructive': 'oklch(0.577 0.245 27.325)',
  '--destructive-foreground': 'oklch(0.985 0.001 106.423)',
  
  // Border and input colors
  '--border': 'oklch(0.923 0.003 48.717)',
  '--input': 'oklch(0.923 0.003 48.717)',
  '--ring': 'oklch(0.709 0.01 56.259)',
  
  // Chart colors
  '--chart-1': 'oklch(0.646 0.222 41.116)',
  '--chart-2': 'oklch(0.6 0.118 184.704)',
  '--chart-3': 'oklch(0.398 0.07 227.392)',
  '--chart-4': 'oklch(0.828 0.189 84.429)',
  '--chart-5': 'oklch(0.769 0.188 70.08)',
  
  // Sidebar colors
  '--sidebar': 'oklch(0.985 0.001 106.423)',
  '--sidebar-foreground': 'oklch(0.147 0.004 49.25)',
  '--sidebar-primary': 'oklch(0.216 0.006 56.043)',
  '--sidebar-primary-foreground': 'oklch(0.985 0.001 106.423)',
  '--sidebar-accent': 'oklch(0.97 0.001 106.424)',
  '--sidebar-accent-foreground': 'oklch(0.216 0.006 56.043)',
  '--sidebar-border': 'oklch(0.923 0.003 48.717)',
  '--sidebar-ring': 'oklch(0.709 0.01 56.259)',
  
  // Border radius
  '--radius': '0.625rem',
} as const;

export const darkTheme = {
  // Base colors
  '--background': 'oklch(0.147 0.004 49.25)',
  '--foreground': 'oklch(0.985 0.001 106.423)',
  
  // Card colors
  '--card': 'oklch(0.216 0.006 56.043)',
  '--card-foreground': 'oklch(0.985 0.001 106.423)',
  
  // Popover colors
  '--popover': 'oklch(0.216 0.006 56.043)',
  '--popover-foreground': 'oklch(0.985 0.001 106.423)',
  
  // Primary colors
  '--primary': 'oklch(0.923 0.003 48.717)',
  '--primary-foreground': 'oklch(0.216 0.006 56.043)',
  
  // Secondary colors
  '--secondary': 'oklch(0.268 0.007 34.298)',
  '--secondary-foreground': 'oklch(0.985 0.001 106.423)',
  
  // Muted colors
  '--muted': 'oklch(0.268 0.007 34.298)',
  '--muted-foreground': 'oklch(0.709 0.01 56.259)',
  
  // Accent colors
  '--accent': 'oklch(0.268 0.007 34.298)',
  '--accent-foreground': 'oklch(0.985 0.001 106.423)',
  
  // Destructive colors
  '--destructive': 'oklch(0.704 0.191 22.216)',
  '--destructive-foreground': 'oklch(0.985 0.001 106.423)',
  
  // Border and input colors
  '--border': 'oklch(1 0 0 / 10%)',
  '--input': 'oklch(1 0 0 / 15%)',
  '--ring': 'oklch(0.553 0.013 58.071)',
  
  // Chart colors
  '--chart-1': 'oklch(0.488 0.243 264.376)',
  '--chart-2': 'oklch(0.696 0.17 162.48)',
  '--chart-3': 'oklch(0.769 0.188 70.08)',
  '--chart-4': 'oklch(0.627 0.265 303.9)',
  '--chart-5': 'oklch(0.645 0.246 16.439)',
  
  // Sidebar colors
  '--sidebar': 'oklch(0.216 0.006 56.043)',
  '--sidebar-foreground': 'oklch(0.985 0.001 106.423)',
  '--sidebar-primary': 'oklch(0.488 0.243 264.376)',
  '--sidebar-primary-foreground': 'oklch(0.985 0.001 106.423)',
  '--sidebar-accent': 'oklch(0.268 0.007 34.298)',
  '--sidebar-accent-foreground': 'oklch(0.985 0.001 106.423)',
  '--sidebar-border': 'oklch(1 0 0 / 10%)',
  '--sidebar-ring': 'oklch(0.553 0.013 58.071)',
  
  // Border radius
  '--radius': '0.625rem',
} as const;

// Theme configuration object
export const themeConfig = {
  light: lightTheme,
  dark: darkTheme,
  defaultTheme: 'system' as ThemeMode,
  enableSystem: true,
  disableTransitionOnChange: false,
  storageKey: 'loni-theme',
  attribute: 'class',
  defaultClass: 'light',
  themes: ['light', 'dark'] as const,
} as const;

// Helper function to apply theme variables
export const applyThemeVariables = (theme: typeof lightTheme | typeof darkTheme) => {
  if (typeof document === 'undefined') return;
  
  const root = document.documentElement;
  Object.entries(theme).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
};

// Helper function to get current theme variables
export const getCurrentThemeVariables = (mode: 'light' | 'dark') => {
  return mode === 'light' ? lightTheme : darkTheme;
};

// CSS-in-JS theme object for styled-components or emotion (if needed)
export const styledTheme = {
  light: {
    colors: {
      background: 'var(--background)',
      foreground: 'var(--foreground)',
      card: 'var(--card)',
      cardForeground: 'var(--card-foreground)',
      popover: 'var(--popover)',
      popoverForeground: 'var(--popover-foreground)',
      primary: 'var(--primary)',
      primaryForeground: 'var(--primary-foreground)',
      secondary: 'var(--secondary)',
      secondaryForeground: 'var(--secondary-foreground)',
      muted: 'var(--muted)',
      mutedForeground: 'var(--muted-foreground)',
      accent: 'var(--accent)',
      accentForeground: 'var(--accent-foreground)',
      destructive: 'var(--destructive)',
      destructiveForeground: 'var(--destructive-foreground)',
      border: 'var(--border)',
      input: 'var(--input)',
      ring: 'var(--ring)',
    },
    spacing: designSystem.spacing,
    borderRadius: designSystem.borderRadius,
    shadows: designSystem.shadows,
    typography: designSystem.typography,
    animations: designSystem.animations,
    breakpoints: designSystem.breakpoints,
    zIndex: designSystem.zIndex,
  },
  dark: {
    colors: {
      background: 'var(--background)',
      foreground: 'var(--foreground)',
      card: 'var(--card)',
      cardForeground: 'var(--card-foreground)',
      popover: 'var(--popover)',
      popoverForeground: 'var(--popover-foreground)',
      primary: 'var(--primary)',
      primaryForeground: 'var(--primary-foreground)',
      secondary: 'var(--secondary)',
      secondaryForeground: 'var(--secondary-foreground)',
      muted: 'var(--muted)',
      mutedForeground: 'var(--muted-foreground)',
      accent: 'var(--accent)',
      accentForeground: 'var(--accent-foreground)',
      destructive: 'var(--destructive)',
      destructiveForeground: 'var(--destructive-foreground)',
      border: 'var(--border)',
      input: 'var(--input)',
      ring: 'var(--ring)',
    },
    spacing: designSystem.spacing,
    borderRadius: designSystem.borderRadius,
    shadows: designSystem.shadows,
    typography: designSystem.typography,
    animations: designSystem.animations,
    breakpoints: designSystem.breakpoints,
    zIndex: designSystem.zIndex,
  },
} as const;

export type StyledTheme = typeof styledTheme.light;
export type ThemeConfig = typeof themeConfig;
